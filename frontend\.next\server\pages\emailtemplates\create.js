/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/emailtemplates/create";
exports.ids = ["pages/emailtemplates/create"];
exports.modules = {

/***/ "./src/styles/Layout.module.css":
/*!**************************************!*\
  !*** ./src/styles/Layout.module.css ***!
  \**************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"container\": \"Layout_container__m2XBw\",\n\t\"nav\": \"Layout_nav__BY5_j\",\n\t\"logo\": \"Layout_logo__A8yk9\",\n\t\"menu\": \"Layout_menu__dRwJa\",\n\t\"main\": \"Layout_main__65zHd\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvc3R5bGVzL0xheW91dC5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcm9udGVuZC8uL3NyYy9zdHlsZXMvTGF5b3V0Lm1vZHVsZS5jc3M/ZTM2MSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJjb250YWluZXJcIjogXCJMYXlvdXRfY29udGFpbmVyX19tMlhCd1wiLFxuXHRcIm5hdlwiOiBcIkxheW91dF9uYXZfX0JZNV9qXCIsXG5cdFwibG9nb1wiOiBcIkxheW91dF9sb2dvX19BOHlrOVwiLFxuXHRcIm1lbnVcIjogXCJMYXlvdXRfbWVudV9fZFJ3SmFcIixcblx0XCJtYWluXCI6IFwiTGF5b3V0X21haW5fXzY1ekhkXCJcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/styles/Layout.module.css\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Femailtemplates%2Fcreate&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cemailtemplates%5Ccreate.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Femailtemplates%2Fcreate&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cemailtemplates%5Ccreate.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_emailtemplates_create_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\emailtemplates\\create.js */ \"./src/pages/emailtemplates/create.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_emailtemplates_create_js__WEBPACK_IMPORTED_MODULE_5__]);\n_src_pages_emailtemplates_create_js__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_create_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_create_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_create_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_create_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_create_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_create_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_create_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_create_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_create_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_create_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_create_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/emailtemplates/create\",\n        pathname: \"/emailtemplates/create\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_emailtemplates_create_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Femailtemplates%2Fcreate&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cemailtemplates%5Ccreate.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/Layout.module.css */ \"./src/styles/Layout.module.css\");\n/* harmony import */ var _styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction Layout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().container),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().nav),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().logo),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            children: \"My App\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().menu),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/smtp-settings\",\n                                    children: \"SMTP Settings\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/customvariables\",\n                                    children: \"Custom Variables\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/emailtemplates\",\n                                    children: \"Email Templates\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().main),\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout.tsx\");\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE2QjtBQUVXO0FBRXpCLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0UsOERBQUNILDBEQUFNQTtrQkFDTCw0RUFBQ0U7WUFBVyxHQUFHQyxTQUFTOzs7Ozs7Ozs7OztBQUc5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICdAL3N0eWxlcy9nbG9iYWxzLmNzcydcbmltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCdcbmltcG9ydCBMYXlvdXQgZnJvbSAnQC9jb21wb25lbnRzL0xheW91dCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8TGF5b3V0PlxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgIDwvTGF5b3V0PlxuICApXG59XG4iXSwibmFtZXMiOlsiTGF5b3V0IiwiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/emailtemplates/create.js":
/*!********************************************!*\
  !*** ./src/pages/emailtemplates/create.js ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateEmailTemplate)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/script */ \"./node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react_quill_dist_quill_snow_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-quill/dist/quill.snow.css */ \"../node_modules/react-quill/dist/quill.snow.css\");\n/* harmony import */ var react_quill_dist_quill_snow_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_quill_dist_quill_snow_css__WEBPACK_IMPORTED_MODULE_7__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_5__]);\naxios__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n// Import the rich text editor with dynamic loading (no SSR)\nconst ReactQuill = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! react-quill */ \"react-quill\", 23)), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\emailtemplates\\\\create.js -> \" + \"react-quill\"\n        ]\n    },\n    ssr: false\n});\n\n// Backend URL from environment variable or default\nconst BACKEND_URL = \"http://localhost:3001\" || 0;\nfunction CreateEmailTemplate() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const quillRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        template_name: \"\",\n        subject: \"\",\n        description: \"\",\n        to: \"\",\n        cc: \"\",\n        bcc: \"\",\n        email_type: \"\"\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customVariables, setCustomVariables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVariables, setSelectedVariables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [aiLoading, setAiLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [aiError, setAiError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Set isClient to true when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n        fetchCustomVariables();\n        // Add jQuery script\n        if (!window.jQuery) {\n            const script = document.createElement(\"script\");\n            script.src = \"https://code.jquery.com/jquery-3.6.0.min.js\";\n            script.onload = initializeJQuery;\n            document.head.appendChild(script);\n        } else {\n            initializeJQuery();\n        }\n        // Cleanup function\n        return ()=>{\n            // Clean up any global references when component unmounts\n            window.quillEditor = null;\n        };\n    }, []);\n    // Add a new useEffect to ensure quillEditor is set after the editor is mounted\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isClient && quillRef.current) {\n            try {\n                // In React Quill, the editor is directly available on the ref\n                window.quillEditor = quillRef.current.editor;\n            } catch (error) {\n                console.error(\"Error accessing Quill editor:\", error);\n            }\n        }\n    }, [\n        isClient,\n        quillRef.current\n    ]);\n    // Add this useEffect to sync the email_type input with formData\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This runs after the component mounts\n        if (isClient) {\n            // Add event listener to the email_type input\n            const emailTypeInput = document.getElementById(\"email_type\");\n            if (emailTypeInput) {\n                emailTypeInput.addEventListener(\"input\", (e)=>{\n                    setFormData((prev)=>({\n                            ...prev,\n                            email_type: e.target.value\n                        }));\n                });\n            }\n        }\n    }, [\n        isClient\n    ]);\n    // Initialize jQuery functionality\n    const initializeJQuery = ()=>{\n        if (window.jQuery) {\n            const $ = window.jQuery;\n            $(document).ready(function() {\n                // Show assistTextareaContainer when \"Get AI Support\" is clicked\n                $(\"#assistBtn\").on(\"click\", function() {\n                    $(\"#assistTextareaContainer\").show();\n                });\n                // Handle \"Generate Options\" button click\n                $(\"#generateOptionsBtn\").on(\"click\", function() {\n                    const prompt = $(\"#assistTextarea\").val().trim();\n                    if (!prompt) {\n                        alert(\"Please enter any prompt or notes\");\n                        return;\n                    }\n                    $(\"#options-container\").html(\"<p>Generating options...</p>\");\n                    tryMultipleEndpoints(prompt);\n                });\n                // Function to try multiple endpoints\n                function tryMultipleEndpoints(prompt) {\n                    const endpoints = [\n                        `/api/email-ai/generate-options`,\n                        `${BACKEND_URL}/api/email-ai/generate-options`,\n                        `${BACKEND_URL}/api/email-ai/direct-generate-options`,\n                        \"http://localhost:3002/api/email-ai/generate-options\" // Standalone server\n                    ];\n                    // Try each endpoint in sequence\n                    function tryNextEndpoint(index) {\n                        if (index >= endpoints.length) {\n                            $(\"#options-container\").html(\"<p>All endpoints failed. Please check server logs.</p>\");\n                            return;\n                        }\n                        const endpoint = endpoints[index];\n                        $(\"#options-container\").html(`<p>Trying endpoint ${index + 1}/${endpoints.length}...</p>`);\n                        $.ajax({\n                            url: endpoint,\n                            method: \"POST\",\n                            contentType: \"application/json\",\n                            data: JSON.stringify({\n                                type: prompt\n                            }),\n                            success: function(response) {\n                                if (response.success) {\n                                    $(\"#options-container\").html(response.data);\n                                } else {\n                                    console.error(`Endpoint ${endpoint} returned error:`, response.message);\n                                    tryNextEndpoint(index + 1);\n                                }\n                            },\n                            error: function(xhr, status, error) {\n                                console.error(`Endpoint ${endpoint} failed:`, error);\n                                tryNextEndpoint(index + 1);\n                            }\n                        });\n                    }\n                    // Start with the first endpoint\n                    tryNextEndpoint(0);\n                }\n                // Handle \"Generate Content\" button click (delegated event for dynamically added button)\n                $(document).on(\"click\", \"#generateContentBtn\", function(e) {\n                    e.preventDefault();\n                    const selected = $('input[name=\"subject_option\"]:checked').val();\n                    const prompt = $(\"#assistTextarea\").val().trim();\n                    if (!selected) {\n                        alert(\"Please select a subject option.\");\n                        return;\n                    }\n                    if (!prompt) {\n                        alert(\"Email type is missing. Please enter a description in the text area.\");\n                        return;\n                    }\n                    const $optionsContainer = $(\"#options-container\");\n                    const $loadingMsg = $('<p id=\"loading-msg\">Generating email content...</p>');\n                    $optionsContainer.append($loadingMsg);\n                    $.ajax({\n                        url: `${BACKEND_URL}/api/email-ai/generate-content`,\n                        method: \"POST\",\n                        contentType: \"application/json\",\n                        data: JSON.stringify({\n                            subject: selected,\n                            type: prompt\n                        }),\n                        success: function(res) {\n                            if (res.success) {\n                                const formattedHtml = res.data.content;\n                                // Use the dedicated function to update the Quill editor\n                                updateQuillContent(formattedHtml);\n                                // Also update the subject field\n                                setFormData((prev)=>({\n                                        ...prev,\n                                        subject: selected\n                                    }));\n                                $(\"#loading-msg\").remove();\n                            } else {\n                                alert(\"Content generation failed: \" + res.message);\n                                $(\"#loading-msg\").remove();\n                            }\n                        },\n                        error: function(xhr) {\n                            alert(\"Something went wrong: \" + (xhr.responseJSON?.message || \"Unknown error\"));\n                            $(\"#loading-msg\").remove();\n                        }\n                    });\n                });\n            });\n        }\n    };\n    // Fetch custom variables from the backend\n    const fetchCustomVariables = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(`${BACKEND_URL}/api/customvariables`);\n            if (response.data && response.data.data) {\n                setCustomVariables(response.data.data);\n            } else if (Array.isArray(response.data)) {\n                // Handle case where data is directly in the response\n                setCustomVariables(response.data);\n            } else {\n                console.error(\"Unexpected response format:\", response.data);\n                setError(\"Failed to load custom variables: Unexpected data format\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching custom variables:\", error);\n            setError(`Failed to load custom variables: ${error.message}`);\n        }\n    };\n    // Handle form input changes\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    // Handle rich text editor changes\n    const handleEditorChange = (content)=>{\n        setFormData((prev)=>({\n                ...prev,\n                description: content\n            }));\n    };\n    // Add a function to update the Quill editor programmatically\n    const updateQuillContent = (content)=>{\n        try {\n            // First try to get the editor from the ref\n            let editor = null;\n            if (quillRef.current) {\n                editor = quillRef.current.editor;\n            }\n            // If not available, try the window global\n            if (!editor && window.quillEditor) {\n                editor = window.quillEditor;\n            }\n            if (editor) {\n                // Clean and format the HTML content\n                const cleanContent = content.replace(/\\n/g, \"<br>\").replace(/<p><br><\\/p>/g, \"<br>\").replace(/\\s{2,}/g, \" \");\n                // Set the content directly to the editor's root\n                editor.root.innerHTML = cleanContent;\n                // Update the form data to match\n                setFormData((prev)=>({\n                        ...prev,\n                        description: cleanContent\n                    }));\n                // Force Quill to recognize the change\n                const event = new Event(\"input\", {\n                    bubbles: true\n                });\n                editor.root.dispatchEvent(event);\n            } else {\n                console.error(\"Quill editor not initialized\");\n                // Fallback: Update formData directly, the editor will render it when available\n                setFormData((prev)=>({\n                        ...prev,\n                        description: content\n                    }));\n            }\n        } catch (error) {\n            console.error(\"Error updating Quill content:\", error);\n            // Fallback: Update formData directly\n            setFormData((prev)=>({\n                    ...prev,\n                    description: content\n                }));\n        }\n    };\n    // Handle custom variable selection\n    const handleVariableSelect = (e)=>{\n        const selectedOptions = Array.from(e.target.selectedOptions, (option)=>parseInt(option.value));\n        setSelectedVariables(selectedOptions);\n    };\n    // Quill editor modules and formats\n    const modules = {\n        toolbar: [\n            [\n                {\n                    \"header\": [\n                        1,\n                        2,\n                        3,\n                        4,\n                        5,\n                        6,\n                        false\n                    ]\n                }\n            ],\n            [\n                \"bold\",\n                \"italic\",\n                \"underline\",\n                \"strike\"\n            ],\n            [\n                {\n                    \"list\": \"ordered\"\n                },\n                {\n                    \"list\": \"bullet\"\n                }\n            ],\n            [\n                {\n                    \"color\": []\n                },\n                {\n                    \"background\": []\n                }\n            ],\n            [\n                \"link\",\n                \"image\"\n            ],\n            [\n                \"clean\"\n            ]\n        ],\n        clipboard: {\n            // toggle to add extra line breaks when pasting HTML:\n            matchVisual: false\n        }\n    };\n    const formats = [\n        \"header\",\n        \"bold\",\n        \"italic\",\n        \"underline\",\n        \"strike\",\n        \"list\",\n        \"bullet\",\n        \"color\",\n        \"background\",\n        \"link\",\n        \"image\"\n    ];\n    // Handle form submission\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validate form data\n        if (!formData.template_name || !formData.subject) {\n            setError(\"Template name and subject are required\");\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Get the email type from the input field\n            const emailType = document.getElementById(\"email_type\")?.value || \"\";\n            // Create the data to send\n            const dataToSend = {\n                ...formData,\n                email_type: emailType,\n                custom_variable: selectedVariables\n            };\n            // Try multiple endpoints if needed\n            const endpoints = [\n                `${BACKEND_URL}/api/email-templates`,\n                `${BACKEND_URL}/api/emailtemplates`\n            ];\n            let response = null;\n            let error = null;\n            for (const endpoint of endpoints){\n                try {\n                    response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(endpoint, dataToSend);\n                    if (response.data.success) {\n                        break;\n                    }\n                } catch (err) {\n                    console.error(`Error with endpoint ${endpoint}:`, err);\n                    error = err;\n                }\n            }\n            if (response && response.data.success) {\n                setSuccess(\"Email template created successfully!\");\n                // Redirect to the list page after a short delay\n                setTimeout(()=>{\n                    router.push(\"/emailtemplates\");\n                }, 2000);\n            } else {\n                setError(response?.data?.message || error?.response?.data?.message || \"Failed to create email template\");\n            }\n        } catch (error) {\n            console.error(\"Error creating email template:\", error);\n            setError(`Failed to create email template: ${error.response?.data?.message || error.message}`);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Add this function to handle generating options\n    const handleGenerateOptions = async ()=>{\n        const assistTextarea = document.getElementById(\"assistTextarea\");\n        const optionsContainer = document.getElementById(\"options-container\");\n        if (!assistTextarea.value.trim()) {\n            alert(\"Please enter a prompt or description first\");\n            return;\n        }\n        setAiLoading(true);\n        setAiError(null);\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(`${BACKEND_URL}/api/email-ai/generate-options`, {\n                type: assistTextarea.value.trim()\n            });\n            if (response.data.success) {\n                // Display the options\n                optionsContainer.innerHTML = response.data.data;\n                // Add event listeners to the radio buttons\n                const radioButtons = optionsContainer.querySelectorAll('input[type=\"radio\"]');\n                radioButtons.forEach((radio)=>{\n                    radio.addEventListener(\"change\", ()=>{\n                        if (radio.checked) {\n                            // Update the subject field with the selected option\n                            setFormData((prev)=>({\n                                    ...prev,\n                                    subject: radio.value\n                                }));\n                        }\n                    });\n                });\n                // Add event listener to the generate content button\n                const generateContentBtn = document.getElementById(\"generateContentBtn\");\n                if (generateContentBtn) {\n                    generateContentBtn.addEventListener(\"click\", handleGenerateContent);\n                }\n            } else {\n                setAiError(response.data.message || \"Failed to generate options\");\n            }\n        } catch (error) {\n            console.error(\"Error generating options:\", error);\n            setAiError(`Error: ${error.response?.data?.message || error.message}`);\n        } finally{\n            setAiLoading(false);\n        }\n    };\n    // Add this function to handle generating content\n    const handleGenerateContent = async ()=>{\n        const selectedOption = document.querySelector('input[name=\"subject_option\"]:checked');\n        const assistTextarea = document.getElementById(\"assistTextarea\");\n        if (!selectedOption) {\n            alert(\"Please select a subject option first\");\n            return;\n        }\n        setAiLoading(true);\n        setAiError(null);\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(`${BACKEND_URL}/api/email-ai/generate-content`, {\n                subject: selectedOption.value,\n                type: assistTextarea.value.trim()\n            });\n            if (response.data.success) {\n                // Update the form data with the generated content\n                setFormData((prev)=>({\n                        ...prev,\n                        subject: selectedOption.value,\n                        description: response.data.data.content\n                    }));\n            } else {\n                setAiError(response.data.message || \"Failed to generate content\");\n            }\n        } catch (error) {\n            console.error(\"Error generating content:\", error);\n            setAiError(`Error: ${error.response?.data?.message || error.message}`);\n        } finally{\n            setAiLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n            style: {\n                marginLeft: \"200px\",\n                padding: \"2rem\",\n                width: \"calc(100% - 200px)\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        justifyContent: \"space-between\",\n                        alignItems: \"center\",\n                        marginBottom: \"1rem\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Create Email Template\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                            lineNumber: 476,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/emailtemplates\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                style: {\n                                    padding: \"0.5rem 1rem\"\n                                },\n                                children: \"Back to List\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                lineNumber: 478,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                            lineNumber: 477,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                    lineNumber: 475,\n                    columnNumber: 11\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        color: \"red\",\n                        marginBottom: \"1rem\",\n                        padding: \"0.5rem\",\n                        border: \"1px solid red\"\n                    },\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                    lineNumber: 483,\n                    columnNumber: 13\n                }, this),\n                success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        color: \"green\",\n                        marginBottom: \"1rem\",\n                        padding: \"0.5rem\",\n                        border: \"1px solid green\"\n                    },\n                    children: success\n                }, void 0, false, {\n                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                    lineNumber: 489,\n                    columnNumber: 13\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    style: {\n                        maxWidth: \"600px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"1rem\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        display: \"block\",\n                                        marginBottom: \"0.5rem\"\n                                    },\n                                    children: \"Template Name *\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    name: \"template_name\",\n                                    value: formData.template_name,\n                                    onChange: handleChange,\n                                    style: {\n                                        width: \"100%\",\n                                        padding: \"0.5rem\"\n                                    },\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 500,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                            lineNumber: 496,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"1rem\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        display: \"block\",\n                                        marginBottom: \"0.5rem\"\n                                    },\n                                    children: \"Subject *\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 511,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    name: \"subject\",\n                                    id: \"add_subject\",\n                                    value: formData.subject,\n                                    onChange: handleChange,\n                                    style: {\n                                        width: \"100%\",\n                                        padding: \"0.5rem\"\n                                    },\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 514,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: \"0.5rem\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        id: \"assistBtn\",\n                                        style: {\n                                            padding: \"0.5rem 1rem\",\n                                            backgroundColor: \"#2271b1\",\n                                            color: \"white\",\n                                            border: \"none\",\n                                            borderRadius: \"4px\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: \"Get AI Support\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                        lineNumber: 524,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 523,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"assistTextareaContainer\",\n                                    style: {\n                                        display: \"none\",\n                                        marginTop: \"10px\",\n                                        padding: \"10px\",\n                                        border: \"1px solid #ddd\",\n                                        borderRadius: \"4px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            id: \"assistTextarea\",\n                                            rows: \"5\",\n                                            placeholder: \"Enter your prompt or notes here...\",\n                                            style: {\n                                                width: \"100%\",\n                                                padding: \"0.5rem\",\n                                                marginBottom: \"10px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                            lineNumber: 556,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            id: \"generateOptionsBtn\",\n                                            style: {\n                                                padding: \"0.5rem 1rem\",\n                                                backgroundColor: \"#2271b1\",\n                                                color: \"white\",\n                                                border: \"none\",\n                                                borderRadius: \"4px\",\n                                                cursor: \"pointer\"\n                                            },\n                                            children: \"Generate Options\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                            lineNumber: 557,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 540,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                            lineNumber: 510,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            id: \"options-container\",\n                            style: {\n                                marginBottom: \"1rem\"\n                            },\n                            children: [\n                                aiLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading options...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 575,\n                                    columnNumber: 29\n                                }, this),\n                                aiError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: \"red\"\n                                    },\n                                    children: aiError\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 576,\n                                    columnNumber: 27\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                            lineNumber: 574,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"1rem\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        display: \"block\",\n                                        marginBottom: \"0.5rem\"\n                                    },\n                                    children: \"Email Content *\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 580,\n                                    columnNumber: 15\n                                }, this),\n                                isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactQuill, {\n                                    theme: \"snow\",\n                                    value: formData.description,\n                                    onChange: handleEditorChange,\n                                    modules: modules,\n                                    formats: formats,\n                                    style: {\n                                        height: \"300px\",\n                                        marginBottom: \"3rem\"\n                                    },\n                                    ref: (el)=>{\n                                        if (el) {\n                                            // The editor instance is directly available on the ref\n                                            quillRef.current = el;\n                                            window.quillEditor = el.editor;\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 584,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                            lineNumber: 579,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"1rem\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        display: \"block\",\n                                        marginBottom: \"0.5rem\"\n                                    },\n                                    children: \"To\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 603,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    name: \"to\",\n                                    value: formData.to,\n                                    onChange: handleChange,\n                                    style: {\n                                        width: \"100%\",\n                                        padding: \"0.5rem\"\n                                    },\n                                    placeholder: \"Recipient email or leave blank to use dynamic recipient\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 606,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                            lineNumber: 602,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"1rem\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        display: \"block\",\n                                        marginBottom: \"0.5rem\"\n                                    },\n                                    children: \"CC\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 617,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    name: \"cc\",\n                                    value: formData.cc,\n                                    onChange: handleChange,\n                                    style: {\n                                        width: \"100%\",\n                                        padding: \"0.5rem\"\n                                    },\n                                    placeholder: \"CC recipients (comma separated)\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 620,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                            lineNumber: 616,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"1rem\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        display: \"block\",\n                                        marginBottom: \"0.5rem\"\n                                    },\n                                    children: \"BCC\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 631,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    name: \"bcc\",\n                                    value: formData.bcc,\n                                    onChange: handleChange,\n                                    style: {\n                                        width: \"100%\",\n                                        padding: \"0.5rem\"\n                                    },\n                                    placeholder: \"BCC recipients (comma separated)\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 634,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                            lineNumber: 630,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"1rem\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        display: \"block\",\n                                        marginBottom: \"0.5rem\"\n                                    },\n                                    children: \"Custom Variables\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 645,\n                                    columnNumber: 15\n                                }, this),\n                                customVariables.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        multiple: true,\n                                        onChange: handleVariableSelect,\n                                        style: {\n                                            width: \"100%\",\n                                            padding: \"0.5rem\",\n                                            height: \"100px\"\n                                        },\n                                        children: customVariables.map((variable)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: variable.id,\n                                                children: variable.cust_vari_name || variable.name\n                                            }, variable.id, false, {\n                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                                lineNumber: 656,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                        lineNumber: 650,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No custom variables available.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                            lineNumber: 664,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/customvariables/create\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                style: {\n                                                    padding: \"0.5rem 1rem\"\n                                                },\n                                                children: \"Create Custom Variable\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                                lineNumber: 666,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                            lineNumber: 665,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 663,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                            lineNumber: 644,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: \"3rem\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    style: {\n                                        padding: \"0.5rem 1rem\",\n                                        backgroundColor: \"#4CAF50\",\n                                        color: \"white\",\n                                        border: \"none\",\n                                        borderRadius: \"4px\",\n                                        cursor: loading ? \"not-allowed\" : \"pointer\",\n                                        marginRight: \"0.5rem\"\n                                    },\n                                    children: loading ? \"Creating...\" : \"Create Template\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 676,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/emailtemplates\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        style: {\n                                            padding: \"0.5rem 1rem\",\n                                            backgroundColor: \"#f44336\",\n                                            color: \"white\",\n                                            border: \"none\",\n                                            borderRadius: \"4px\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                        lineNumber: 692,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                                    lineNumber: 691,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                            lineNumber: 675,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n                    lineNumber: 494,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n            lineNumber: 474,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\create.js\",\n        lineNumber: 472,\n        columnNumber: 7\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/emailtemplates/create.js\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-quill":
/*!******************************!*\
  !*** external "react-quill" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-quill");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-quill"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Femailtemplates%2Fcreate&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cemailtemplates%5Ccreate.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();