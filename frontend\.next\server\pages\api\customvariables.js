"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/customvariables";
exports.ids = ["pages/api/customvariables"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcustomvariables&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ccustomvariables%5Cindex.js&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcustomvariables&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ccustomvariables%5Cindex.js&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_customvariables_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\customvariables\\index.js */ \"(api)/./src/pages/api/customvariables/index.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_customvariables_index_js__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_customvariables_index_js__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/customvariables\",\n        pathname: \"/api/customvariables\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_customvariables_index_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcustomvariables&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ccustomvariables%5Cindex.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/customvariables/index.js":
/*!************************************************!*\
  !*** ./src/pages/api/customvariables/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\nasync function handler(req, res) {\n    if (req.method === \"GET\") {\n        try {\n            // Set cache control headers to prevent caching\n            res.setHeader(\"Cache-Control\", \"no-store, no-cache, must-revalidate, proxy-revalidate\");\n            res.setHeader(\"Pragma\", \"no-cache\");\n            res.setHeader(\"Expires\", \"0\");\n            res.setHeader(\"Surrogate-Control\", \"no-store\");\n            // Add a unique timestamp to prevent 304 responses\n            res.setHeader(\"Last-Modified\", new Date().toUTCString());\n            // Log the incoming request query parameters\n            console.log(\"Frontend API received query params:\", req.query);\n            // Use a fallback value if BACKEND_URL is not defined\n            const BACKEND_URL = process.env.BACKEND_URL || \"http://localhost:3001\";\n            console.log(\"Using BACKEND_URL:\", BACKEND_URL);\n            // Add timeout to the fetch request\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 5000);\n            // Add a timestamp to the query parameters to prevent caching\n            const queryParams = {\n                ...req.query,\n                _t: Date.now()\n            };\n            const queryString = new URLSearchParams(queryParams).toString();\n            const url = `${BACKEND_URL}/api/customvariables${queryString ? `?${queryString}` : \"\"}`;\n            console.log(\"Fetching from backend URL:\", url);\n            const response = await fetch(url, {\n                headers: {\n                    \"Accept\": \"application/json\",\n                    \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                    \"Pragma\": \"no-cache\",\n                    \"Expires\": \"0\"\n                },\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            const responseText = await response.text();\n            let data;\n            let parseError = null;\n            try {\n                // Try to parse the response as JSON\n                data = JSON.parse(responseText);\n            } catch (error) {\n                // If parsing fails, store the error and raw response\n                parseError = String(error);\n                data = {\n                    success: false,\n                    message: \"Failed to parse backend response\"\n                };\n            }\n            if (!response.ok) {\n                console.error(`Backend error (${response.status}):`, responseText);\n                return res.status(response.status).json({\n                    success: false,\n                    message: `Backend error: ${response.status} ${response.statusText}`,\n                    debug: {\n                        responseText,\n                        parseError,\n                        backendUrl: url\n                    }\n                });\n            }\n            console.log(\"Backend response:\", data);\n            // Return the data directly without wrapping it\n            return res.status(200).json(data);\n        } catch (error) {\n            console.error(\"Error fetching custom variables:\", error);\n            // Check if it's an abort error (timeout)\n            if (error instanceof Error && error.name === \"AbortError\") {\n                return res.status(504).json({\n                    success: false,\n                    message: \"Backend connection timed out. Please check if the backend server is running.\",\n                    debug: {\n                        error: String(error)\n                    }\n                });\n            }\n            return res.status(500).json({\n                success: false,\n                message: `Failed to fetch custom variables from backend: ${error instanceof Error ? error.message : String(error)}`,\n                debug: {\n                    error: String(error)\n                }\n            });\n        }\n    } else {\n        res.setHeader(\"Allow\", [\n            \"GET\"\n        ]);\n        res.status(405).json({\n            success: false,\n            message: `Method ${req.method} Not Allowed`\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/customvariables/index.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcustomvariables&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ccustomvariables%5Cindex.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();