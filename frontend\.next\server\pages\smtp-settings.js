/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/smtp-settings";
exports.ids = ["pages/smtp-settings"];
exports.modules = {

/***/ "./src/styles/Layout.module.css":
/*!**************************************!*\
  !*** ./src/styles/Layout.module.css ***!
  \**************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"container\": \"Layout_container__m2XBw\",\n\t\"nav\": \"Layout_nav__BY5_j\",\n\t\"logo\": \"Layout_logo__A8yk9\",\n\t\"menu\": \"Layout_menu__dRwJa\",\n\t\"main\": \"Layout_main__65zHd\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvc3R5bGVzL0xheW91dC5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcm9udGVuZC8uL3NyYy9zdHlsZXMvTGF5b3V0Lm1vZHVsZS5jc3M/ZTM2MSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJjb250YWluZXJcIjogXCJMYXlvdXRfY29udGFpbmVyX19tMlhCd1wiLFxuXHRcIm5hdlwiOiBcIkxheW91dF9uYXZfX0JZNV9qXCIsXG5cdFwibG9nb1wiOiBcIkxheW91dF9sb2dvX19BOHlrOVwiLFxuXHRcIm1lbnVcIjogXCJMYXlvdXRfbWVudV9fZFJ3SmFcIixcblx0XCJtYWluXCI6IFwiTGF5b3V0X21haW5fXzY1ekhkXCJcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/styles/Layout.module.css\n");

/***/ }),

/***/ "./src/styles/SmtpSettings.module.css":
/*!********************************************!*\
  !*** ./src/styles/SmtpSettings.module.css ***!
  \********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"container\": \"SmtpSettings_container__vr8HQ\",\n\t\"title\": \"SmtpSettings_title__AvpTa\",\n\t\"form\": \"SmtpSettings_form__jcGN2\",\n\t\"formGroup\": \"SmtpSettings_formGroup__XOsfn\",\n\t\"submitButton\": \"SmtpSettings_submitButton__kjUsF\",\n\t\"message\": \"SmtpSettings_message__NfpjD\",\n\t\"success\": \"SmtpSettings_success__EW97i\",\n\t\"error\": \"SmtpSettings_error__Ivx9n\",\n\t\"loading\": \"SmtpSettings_loading__cucDR\",\n\t\"debugSection\": \"SmtpSettings_debugSection__22MJ8\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvc3R5bGVzL1NtdHBTZXR0aW5ncy5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9zcmMvc3R5bGVzL1NtdHBTZXR0aW5ncy5tb2R1bGUuY3NzP2UyYTMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiY29udGFpbmVyXCI6IFwiU210cFNldHRpbmdzX2NvbnRhaW5lcl9fdnI4SFFcIixcblx0XCJ0aXRsZVwiOiBcIlNtdHBTZXR0aW5nc190aXRsZV9fQXZwVGFcIixcblx0XCJmb3JtXCI6IFwiU210cFNldHRpbmdzX2Zvcm1fX2pjR04yXCIsXG5cdFwiZm9ybUdyb3VwXCI6IFwiU210cFNldHRpbmdzX2Zvcm1Hcm91cF9fWE9zZm5cIixcblx0XCJzdWJtaXRCdXR0b25cIjogXCJTbXRwU2V0dGluZ3Nfc3VibWl0QnV0dG9uX19ralVzRlwiLFxuXHRcIm1lc3NhZ2VcIjogXCJTbXRwU2V0dGluZ3NfbWVzc2FnZV9fTmZwakRcIixcblx0XCJzdWNjZXNzXCI6IFwiU210cFNldHRpbmdzX3N1Y2Nlc3NfX0VXOTdpXCIsXG5cdFwiZXJyb3JcIjogXCJTbXRwU2V0dGluZ3NfZXJyb3JfX0l2eDluXCIsXG5cdFwibG9hZGluZ1wiOiBcIlNtdHBTZXR0aW5nc19sb2FkaW5nX19jdWNEUlwiLFxuXHRcImRlYnVnU2VjdGlvblwiOiBcIlNtdHBTZXR0aW5nc19kZWJ1Z1NlY3Rpb25fXzIyTUo4XCJcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/styles/SmtpSettings.module.css\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fsmtp-settings&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Csmtp-settings.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fsmtp-settings&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Csmtp-settings.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_smtp_settings_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\smtp-settings.tsx */ \"./src/pages/smtp-settings.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_smtp_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_smtp_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_smtp_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_smtp_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_smtp_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_smtp_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_smtp_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_smtp_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_smtp_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_smtp_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_smtp_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/smtp-settings\",\n        pathname: \"/smtp-settings\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_smtp_settings_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fsmtp-settings&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Csmtp-settings.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/Layout.module.css */ \"./src/styles/Layout.module.css\");\n/* harmony import */ var _styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction Layout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().container),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().nav),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().logo),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            children: \"My App\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().menu),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/smtp-settings\",\n                                    children: \"SMTP Settings\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/customvariables\",\n                                    children: \"Custom Variables\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/emailtemplates\",\n                                    children: \"Email Templates\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().main),\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout.tsx\");\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE2QjtBQUVXO0FBRXpCLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0UsOERBQUNILDBEQUFNQTtrQkFDTCw0RUFBQ0U7WUFBVyxHQUFHQyxTQUFTOzs7Ozs7Ozs7OztBQUc5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICdAL3N0eWxlcy9nbG9iYWxzLmNzcydcbmltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCdcbmltcG9ydCBMYXlvdXQgZnJvbSAnQC9jb21wb25lbnRzL0xheW91dCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8TGF5b3V0PlxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgIDwvTGF5b3V0PlxuICApXG59XG4iXSwibmFtZXMiOlsiTGF5b3V0IiwiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/smtp-settings.tsx":
/*!*************************************!*\
  !*** ./src/pages/smtp-settings.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SmtpSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/api */ \"./src/services/api.ts\");\n/* harmony import */ var _styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../styles/SmtpSettings.module.css */ \"./src/styles/SmtpSettings.module.css\");\n/* harmony import */ var _styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction SubmitButton({ isConnected, isLoading }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: \"submit\",\n        className: (_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().submitButton),\n        disabled: !isConnected || isLoading,\n        children: isLoading ? \"Saving...\" : \"Save Settings\"\n    }, void 0, false, {\n        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\nfunction SmtpSettings() {\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mail_driver: \"\",\n        mail_host: \"\",\n        mail_port: \"\",\n        mail_username: \"\",\n        mail_password: \"\",\n        mail_from_name: \"\",\n        mail_from_email: \"\",\n        sendgrid_api_key: \"\"\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        text: \"\",\n        type: \"\"\n    });\n    const [backendStatus, setBackendStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isConnected: false,\n        message: \"Checking connection...\",\n        lastChecked: null\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkBackendConnection();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (backendStatus.isConnected) {\n            fetchSettings();\n        }\n    }, [\n        backendStatus.isConnected\n    ]);\n    const checkBackendConnection = async ()=>{\n        try {\n            setBackendStatus((prev)=>({\n                    ...prev,\n                    message: \"Checking connection...\"\n                }));\n            const isAvailable = await _services_api__WEBPACK_IMPORTED_MODULE_3__.api.checkBackend();\n            setBackendStatus({\n                isConnected: isAvailable,\n                message: isAvailable ? \"Connected to backend\" : \"Backend server is not responding\",\n                lastChecked: new Date().toISOString()\n            });\n        } catch (error) {\n            console.error(\"Backend connection check failed:\", error);\n            setBackendStatus({\n                isConnected: false,\n                message: `Cannot connect to backend: ${error instanceof Error ? error.message : \"Unknown error\"}`,\n                lastChecked: new Date().toISOString()\n            });\n        }\n    };\n    const fetchSettings = async ()=>{\n        if (!backendStatus.isConnected) {\n            setMessage({\n                text: \"Cannot fetch settings: Backend is not connected\",\n                type: \"error\"\n            });\n            return;\n        }\n        try {\n            setLoading(true);\n            setMessage({\n                text: \"Loading settings...\",\n                type: \"info\"\n            });\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.api.get(\"/api/smtp-settings\");\n            if (response.success && response.data) {\n                setSettings(response.data);\n                setMessage({\n                    text: \"Settings loaded successfully\",\n                    type: \"success\"\n                });\n            } else {\n                setMessage({\n                    text: response.message || \"Failed to load settings\",\n                    type: \"error\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching settings:\", error);\n            setMessage({\n                text: `Failed to load settings: ${error instanceof Error ? error.message : \"Unknown error\"}`,\n                type: \"error\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setSettings((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!backendStatus.isConnected) {\n            setMessage({\n                text: \"Cannot save settings: Backend is not connected\",\n                type: \"error\"\n            });\n            return;\n        }\n        setMessage({\n            text: \"Saving settings...\",\n            type: \"info\"\n        });\n        setLoading(true);\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.api.post(\"/api/smtp-settings\", settings);\n            if (response.success) {\n                setMessage({\n                    text: \"Settings saved successfully\",\n                    type: \"success\"\n                });\n            } else {\n                setMessage({\n                    text: response.message || \"Failed to save settings\",\n                    type: \"error\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Error saving settings:\", error);\n            setMessage({\n                text: `Failed to save settings: ${error instanceof Error ? error.message : \"Unknown error\"}`,\n                type: \"error\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SMTP Settings\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Configure SMTP settings\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().container),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: (_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().title),\n                        children: \"SMTP Settings\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    !backendStatus.isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${(_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().message)} ${(_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().error)}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Backend Connection Error:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            \" \",\n                            backendStatus.message,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().connectionHelp),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Please make sure:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"The backend server is running on port 3001\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"There are no firewall or network issues\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"The CORS settings are properly configured\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: checkBackendConnection,\n                                        className: (_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().retryButton),\n                                        children: \"Retry Connection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this),\n                    message.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${(_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().message)} ${(_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default())[message.type]}`,\n                        children: message.text\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: (_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().form),\n                        children: [\n                            [\n                                {\n                                    label: \"Mail Driver\",\n                                    name: \"mail_driver\",\n                                    type: \"text\"\n                                },\n                                {\n                                    label: \"Mail Host\",\n                                    name: \"mail_host\",\n                                    type: \"text\"\n                                },\n                                {\n                                    label: \"Mail Port\",\n                                    name: \"mail_port\",\n                                    type: \"text\"\n                                },\n                                {\n                                    label: \"Mail Username\",\n                                    name: \"mail_username\",\n                                    type: \"text\"\n                                },\n                                {\n                                    label: \"Mail Password\",\n                                    name: \"mail_password\",\n                                    type: \"password\"\n                                },\n                                {\n                                    label: \"From Name\",\n                                    name: \"mail_from_name\",\n                                    type: \"text\"\n                                },\n                                {\n                                    label: \"From Email\",\n                                    name: \"mail_from_email\",\n                                    type: \"email\"\n                                },\n                                {\n                                    label: \"SendGrid API Key (Optional)\",\n                                    name: \"sendgrid_api_key\",\n                                    type: \"password\"\n                                }\n                            ].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().formGroup),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: field.name,\n                                            children: field.label\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: field.type,\n                                            id: field.name,\n                                            name: field.name,\n                                            value: settings[field.name],\n                                            onChange: handleChange,\n                                            required: [\n                                                \"mail_driver\",\n                                                \"mail_host\",\n                                                \"mail_port\",\n                                                \"mail_username\",\n                                                \"mail_password\"\n                                            ].includes(field.name),\n                                            disabled: !backendStatus.isConnected || loading\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, field.name, true, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().formActions),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SubmitButton, {\n                                    isConnected: backendStatus.isConnected,\n                                    isLoading: loading\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/smtp-settings.tsx\n");

/***/ }),

/***/ "./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api)\n/* harmony export */ });\n// API service for making requests to the backend\n// Use environment variable for backend URL or default to localhost:3001\nconst BASE_URL = \"http://localhost:3001\" || 0;\n// Helper function to check if the backend is available\nconst checkBackendAvailability = async ()=>{\n    try {\n        // Create a controller to set a timeout\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 5000);\n        const response = await fetch(`${BASE_URL}/api/test`, {\n            method: \"GET\",\n            headers: {\n                \"Accept\": \"application/json\"\n            },\n            signal: controller.signal,\n            // Add cache: 'no-store' to prevent caching\n            cache: \"no-store\"\n        });\n        clearTimeout(timeoutId);\n        if (response.ok) {\n            return true;\n        }\n        console.error(`Backend check failed with status: ${response.status}`);\n        return false;\n    } catch (error) {\n        console.error(\"Backend availability check failed:\", error);\n        return false;\n    }\n};\nconst api = {\n    // Export the function with the correct name\n    checkBackend: checkBackendAvailability,\n    get: async (url)=>{\n        try {\n            // Add timeout to the fetch request\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 5000);\n            const res = await fetch(`${BASE_URL}${url}`, {\n                headers: {\n                    \"Accept\": \"application/json\"\n                },\n                signal: controller.signal,\n                // Add cache: 'no-store' to prevent caching\n                cache: \"no-store\"\n            });\n            clearTimeout(timeoutId);\n            if (!res.ok) {\n                const errorText = await res.text();\n                console.error(`API Error (${res.status}):`, errorText);\n                return {\n                    success: false,\n                    message: `API Error: ${res.status} ${res.statusText}`,\n                    error: errorText\n                };\n            }\n            const data = await res.json();\n            return data;\n        } catch (error) {\n            console.error(\"API fetch error:\", error);\n            // Check if it's an abort error (timeout)\n            if (error instanceof Error && error.name === \"AbortError\") {\n                return {\n                    success: false,\n                    message: \"Request timed out. Please check if the backend server is running.\"\n                };\n            }\n            return {\n                success: false,\n                message: error instanceof Error ? error.message : \"Failed to fetch\"\n            };\n        }\n    },\n    post: async (url, data)=>{\n        try {\n            // Add timeout to the fetch request\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 5000);\n            const res = await fetch(`${BASE_URL}${url}`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Accept\": \"application/json\"\n                },\n                body: JSON.stringify(data),\n                signal: controller.signal,\n                // Add cache: 'no-store' to prevent caching\n                cache: \"no-store\"\n            });\n            clearTimeout(timeoutId);\n            let responseData;\n            try {\n                responseData = await res.json();\n            } catch (e) {\n                const text = await res.text();\n                responseData = {\n                    success: false,\n                    message: text\n                };\n            }\n            if (!res.ok) {\n                console.error(`API Error (${res.status}):`, responseData);\n                return {\n                    success: false,\n                    message: responseData.message || `API Error: ${res.status} ${res.statusText}`,\n                    error: responseData,\n                    status: res.status\n                };\n            }\n            return responseData;\n        } catch (error) {\n            console.error(\"API post error:\", error);\n            // Check if it's an abort error (timeout)\n            if (error instanceof Error && error.name === \"AbortError\") {\n                return {\n                    success: false,\n                    message: \"Request timed out. Please check if the backend server is running.\"\n                };\n            }\n            return {\n                success: false,\n                message: error instanceof Error ? error.message : \"Failed to fetch\"\n            };\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/services/api.ts\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fsmtp-settings&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Csmtp-settings.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();