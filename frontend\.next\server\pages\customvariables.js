/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/customvariables";
exports.ids = ["pages/customvariables"];
exports.modules = {

/***/ "./src/styles/Layout.module.css":
/*!**************************************!*\
  !*** ./src/styles/Layout.module.css ***!
  \**************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"container\": \"Layout_container__m2XBw\",\n\t\"nav\": \"Layout_nav__BY5_j\",\n\t\"logo\": \"Layout_logo__A8yk9\",\n\t\"menu\": \"Layout_menu__dRwJa\",\n\t\"main\": \"Layout_main__65zHd\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvc3R5bGVzL0xheW91dC5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcm9udGVuZC8uL3NyYy9zdHlsZXMvTGF5b3V0Lm1vZHVsZS5jc3M/ZTM2MSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJjb250YWluZXJcIjogXCJMYXlvdXRfY29udGFpbmVyX19tMlhCd1wiLFxuXHRcIm5hdlwiOiBcIkxheW91dF9uYXZfX0JZNV9qXCIsXG5cdFwibG9nb1wiOiBcIkxheW91dF9sb2dvX19BOHlrOVwiLFxuXHRcIm1lbnVcIjogXCJMYXlvdXRfbWVudV9fZFJ3SmFcIixcblx0XCJtYWluXCI6IFwiTGF5b3V0X21haW5fXzY1ekhkXCJcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/styles/Layout.module.css\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcustomvariables&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Ccustomvariables%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcustomvariables&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Ccustomvariables%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_customvariables_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\customvariables\\index.js */ \"./src/pages/customvariables/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_customvariables_index_js__WEBPACK_IMPORTED_MODULE_5__]);\n_src_pages_customvariables_index_js__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_customvariables_index_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_customvariables_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_customvariables_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_customvariables_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_customvariables_index_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_customvariables_index_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_customvariables_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_customvariables_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_customvariables_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_customvariables_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_customvariables_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/customvariables\",\n        pathname: \"/customvariables\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_customvariables_index_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcustomvariables&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Ccustomvariables%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/Layout.module.css */ \"./src/styles/Layout.module.css\");\n/* harmony import */ var _styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction Layout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().container),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().nav),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().logo),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            children: \"My App\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().menu),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/smtp-settings\",\n                                    children: \"SMTP Settings\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/customvariables\",\n                                    children: \"Custom Variables\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/emailtemplates\",\n                                    children: \"Email Templates\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().main),\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout.tsx\");\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE2QjtBQUVXO0FBRXpCLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0UsOERBQUNILDBEQUFNQTtrQkFDTCw0RUFBQ0U7WUFBVyxHQUFHQyxTQUFTOzs7Ozs7Ozs7OztBQUc5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICdAL3N0eWxlcy9nbG9iYWxzLmNzcydcbmltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCdcbmltcG9ydCBMYXlvdXQgZnJvbSAnQC9jb21wb25lbnRzL0xheW91dCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8TGF5b3V0PlxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgIDwvTGF5b3V0PlxuICApXG59XG4iXSwibmFtZXMiOlsiTGF5b3V0IiwiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/customvariables/index.js":
/*!********************************************!*\
  !*** ./src/pages/customvariables/index.js ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CustomVariablesList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_2__]);\naxios__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nfunction CustomVariablesList() {\n    const [customVariables, setCustomVariables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [debouncedSearch, setDebouncedSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        totalPages: 1,\n        total: 0,\n        limit: 10\n    });\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Debounce search input\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handler = setTimeout(()=>{\n            setDebouncedSearch(search);\n        }, 500);\n        return ()=>clearTimeout(handler);\n    }, [\n        search\n    ]);\n    // When debouncedSearch changes → reset page to 1\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPagination((prev)=>({\n                ...prev,\n                page: 1\n            }));\n    }, [\n        debouncedSearch\n    ]);\n    const fetchCustomVariables = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const res = await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"/api/customvariables\", {\n                params: {\n                    search: debouncedSearch,\n                    page: pagination.page,\n                    limit: pagination.limit\n                }\n            });\n            const data = res.data?.data || res.data;\n            if (!Array.isArray(data)) throw new Error(\"Unexpected response format\");\n            setCustomVariables(data);\n            if (res.data.pagination) {\n                setPagination((prev)=>({\n                        ...prev,\n                        totalPages: res.data.pagination.totalPages || 1,\n                        total: res.data.pagination.total || 0\n                    }));\n            }\n        } catch (err) {\n            const message = err.response?.data?.message || err.response?.statusText || err.message || \"Error fetching data\";\n            setError(message);\n            setCustomVariables([]);\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        debouncedSearch,\n        pagination.page,\n        pagination.limit\n    ]);\n    // Watch debouncedSearch + page, and fetch data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCustomVariables();\n    }, [\n        fetchCustomVariables\n    ]);\n    const handleDelete = async (id)=>{\n        if (!confirm(\"Are you sure you want to delete this custom variable?\")) return;\n        try {\n            setLoading(true);\n            const res = await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"][\"delete\"](`/api/customvariables/${id}`);\n            alert(res.data.message || \"Deleted successfully\");\n            fetchCustomVariables();\n        } catch (err) {\n            alert(err.response?.data?.message || err.message || \"Error deleting\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handlePageChange = (direction)=>{\n        setPagination((prev)=>({\n                ...prev,\n                page: Math.max(1, prev.page + direction)\n            }));\n    };\n    const handleClearSearch = ()=>{\n        setSearch(\"\");\n        setDebouncedSearch(\"\");\n        setPagination((prev)=>({\n                ...prev,\n                page: 1\n            }));\n    };\n    const tableCellStyle = {\n        padding: \"12px\",\n        border: \"1px solid #ddd\"\n    };\n    const buttonStyle = {\n        padding: \"6px 12px\",\n        border: \"none\",\n        borderRadius: \"4px\",\n        cursor: \"pointer\",\n        color: \"white\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"20px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"Custom Variables\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: \"Custom Variables\"\n            }, void 0, false, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    margin: \"20px 0\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>router.push(\"/customvariables/create\"),\n                    style: {\n                        ...buttonStyle,\n                        backgroundColor: \"#4CAF50\",\n                        padding: \"8px 16px\"\n                    },\n                    children: \"Create New Template\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: \"red\",\n                    marginBottom: \"1rem\",\n                    padding: \"0.5rem\",\n                    backgroundColor: \"#ffeeee\",\n                    border: \"1px solid #ffcccc\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Error:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this),\n                    \" \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    margin: \"20px 0\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: \"40px\",\n                            height: \"40px\",\n                            border: \"4px solid #f3f3f3\",\n                            borderTop: \"4px solid #3498db\",\n                            borderRadius: \"50%\",\n                            animation: \"spin 1s linear infinite\",\n                            margin: \"0 auto\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                        children: `\r\n              @keyframes spin {\r\n                0% { transform: rotate(0deg); }\r\n                100% { transform: rotate(360deg); }\r\n              }\r\n            `\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                style: {\n                    width: \"100%\",\n                    borderCollapse: \"collapse\",\n                    marginTop: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            style: {\n                                backgroundColor: \"#f2f2f2\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    style: tableCellStyle,\n                                    children: \"ID\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    style: tableCellStyle,\n                                    children: \"Variable Name\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    style: tableCellStyle,\n                                    children: \"Table Name\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    style: tableCellStyle,\n                                    children: \"Table Column\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    style: tableCellStyle,\n                                    children: \"Status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    style: tableCellStyle,\n                                    children: \"Created At\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    style: tableCellStyle,\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        children: customVariables.length > 0 ? customVariables.map((v)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        style: tableCellStyle,\n                                        children: v.id\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                        lineNumber: 184,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        style: tableCellStyle,\n                                        children: v.cust_vari_name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                        lineNumber: 185,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        style: tableCellStyle,\n                                        children: v.table_name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                        lineNumber: 186,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        style: tableCellStyle,\n                                        children: v.table_column\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                        lineNumber: 187,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        style: tableCellStyle,\n                                        children: v.status === 1 ? \"Active\" : \"Inactive\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                        lineNumber: 188,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        style: tableCellStyle,\n                                        children: v.created_at ? new Date(v.created_at).toLocaleDateString() : \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                        lineNumber: 189,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        style: tableCellStyle,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push(`/customvariables/edit/${v.id}`),\n                                                style: {\n                                                    ...buttonStyle,\n                                                    backgroundColor: \"#2196F3\",\n                                                    marginRight: \"8px\"\n                                                },\n                                                children: \"Edit\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                                lineNumber: 193,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDelete(v.id),\n                                                style: {\n                                                    ...buttonStyle,\n                                                    backgroundColor: \"#f44336\"\n                                                },\n                                                children: \"Delete\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                                lineNumber: 199,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                        lineNumber: 192,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, v.id, true, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                lineNumber: 183,\n                                columnNumber: 17\n                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                colSpan: \"7\",\n                                style: {\n                                    padding: \"12px\",\n                                    textAlign: \"center\"\n                                },\n                                children: \"No custom variables found\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                                lineNumber: 210,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                            lineNumber: 209,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginTop: \"20px\",\n                    display: \"flex\",\n                    justifyContent: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handlePageChange(-1),\n                        disabled: pagination.page <= 1,\n                        style: {\n                            ...buttonStyle,\n                            backgroundColor: \"#2196F3\",\n                            marginRight: \"10px\"\n                        },\n                        children: \"Previous\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            alignSelf: \"center\"\n                        },\n                        children: [\n                            \"Page \",\n                            pagination.page,\n                            \" of \",\n                            pagination.totalPages\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handlePageChange(1),\n                        disabled: pagination.page >= pagination.totalPages,\n                        style: {\n                            ...buttonStyle,\n                            backgroundColor: \"#2196F3\",\n                            marginLeft: \"10px\"\n                        },\n                        children: \"Next\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\customvariables\\\\index.js\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvY3VzdG9tdmFyaWFibGVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXdEO0FBQy9CO0FBQ2M7QUFDWDtBQUViLFNBQVNNO0lBQ3RCLE1BQU0sQ0FBQ0MsaUJBQWlCQyxtQkFBbUIsR0FBR1AsK0NBQVFBLENBQUMsRUFBRTtJQUN6RCxNQUFNLENBQUNRLFFBQVFDLFVBQVUsR0FBR1QsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDVSxpQkFBaUJDLG1CQUFtQixHQUFHWCwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUNZLFNBQVNDLFdBQVcsR0FBR2IsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDYyxPQUFPQyxTQUFTLEdBQUdmLCtDQUFRQSxDQUFDO0lBQ25DLE1BQU0sQ0FBQ2dCLFlBQVlDLGNBQWMsR0FBR2pCLCtDQUFRQSxDQUFDO1FBQzNDa0IsTUFBTTtRQUNOQyxZQUFZO1FBQ1pDLE9BQU87UUFDUEMsT0FBTztJQUNUO0lBRUEsTUFBTUMsU0FBU25CLHNEQUFTQTtJQUV4Qix3QkFBd0I7SUFDeEJKLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXdCLFVBQVVDLFdBQVc7WUFDekJiLG1CQUFtQkg7UUFDckIsR0FBRztRQUNILE9BQU8sSUFBTWlCLGFBQWFGO0lBQzVCLEdBQUc7UUFBQ2Y7S0FBTztJQUVYLGlEQUFpRDtJQUNqRFQsZ0RBQVNBLENBQUM7UUFDUmtCLGNBQWMsQ0FBQ1MsT0FBVTtnQkFBRSxHQUFHQSxJQUFJO2dCQUFFUixNQUFNO1lBQUU7SUFDOUMsR0FBRztRQUFDUjtLQUFnQjtJQUVwQixNQUFNaUIsdUJBQXVCMUIsa0RBQVdBLENBQUM7UUFDdkNZLFdBQVc7UUFDWEUsU0FBUztRQUVULElBQUk7WUFDRixNQUFNYSxNQUFNLE1BQU0xQixpREFBUyxDQUFDLHdCQUF3QjtnQkFDbEQ0QixRQUFRO29CQUNOdEIsUUFBUUU7b0JBQ1JRLE1BQU1GLFdBQVdFLElBQUk7b0JBQ3JCRyxPQUFPTCxXQUFXSyxLQUFLO2dCQUN6QjtZQUNGO1lBRUEsTUFBTVUsT0FBT0gsSUFBSUcsSUFBSSxFQUFFQSxRQUFRSCxJQUFJRyxJQUFJO1lBQ3ZDLElBQUksQ0FBQ0MsTUFBTUMsT0FBTyxDQUFDRixPQUFPLE1BQU0sSUFBSUcsTUFBTTtZQUUxQzNCLG1CQUFtQndCO1lBQ25CLElBQUlILElBQUlHLElBQUksQ0FBQ2YsVUFBVSxFQUFFO2dCQUN2QkMsY0FBYyxDQUFDUyxPQUFVO3dCQUN2QixHQUFHQSxJQUFJO3dCQUNQUCxZQUFZUyxJQUFJRyxJQUFJLENBQUNmLFVBQVUsQ0FBQ0csVUFBVSxJQUFJO3dCQUM5Q0MsT0FBT1EsSUFBSUcsSUFBSSxDQUFDZixVQUFVLENBQUNJLEtBQUssSUFBSTtvQkFDdEM7WUFDRjtRQUNGLEVBQUUsT0FBT2UsS0FBSztZQUNaLE1BQU1DLFVBQ0pELElBQUlFLFFBQVEsRUFBRU4sTUFBTUssV0FDcEJELElBQUlFLFFBQVEsRUFBRUMsY0FDZEgsSUFBSUMsT0FBTyxJQUNYO1lBQ0ZyQixTQUFTcUI7WUFDVDdCLG1CQUFtQixFQUFFO1FBQ3ZCLFNBQVU7WUFDUk0sV0FBVztRQUNiO0lBQ0YsR0FBRztRQUFDSDtRQUFpQk0sV0FBV0UsSUFBSTtRQUFFRixXQUFXSyxLQUFLO0tBQUM7SUFFdkQsK0NBQStDO0lBQy9DdEIsZ0RBQVNBLENBQUM7UUFDUjRCO0lBQ0YsR0FBRztRQUFDQTtLQUFxQjtJQUV6QixNQUFNWSxlQUFlLE9BQU9DO1FBQzFCLElBQUksQ0FBQ0MsUUFBUSwwREFBMEQ7UUFFdkUsSUFBSTtZQUNGNUIsV0FBVztZQUNYLE1BQU1lLE1BQU0sTUFBTTFCLHVEQUFZLENBQUMsQ0FBQyxxQkFBcUIsRUFBRXNDLEdBQUcsQ0FBQztZQUMzREcsTUFBTWYsSUFBSUcsSUFBSSxDQUFDSyxPQUFPLElBQUk7WUFDMUJUO1FBQ0YsRUFBRSxPQUFPUSxLQUFLO1lBQ1pRLE1BQU1SLElBQUlFLFFBQVEsRUFBRU4sTUFBTUssV0FBV0QsSUFBSUMsT0FBTyxJQUFJO1FBQ3RELFNBQVU7WUFDUnZCLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTStCLG1CQUFtQixDQUFDQztRQUN4QjVCLGNBQWMsQ0FBQ1MsT0FBVTtnQkFDdkIsR0FBR0EsSUFBSTtnQkFDUFIsTUFBTTRCLEtBQUtDLEdBQUcsQ0FBQyxHQUFHckIsS0FBS1IsSUFBSSxHQUFHMkI7WUFDaEM7SUFDRjtJQUVBLE1BQU1HLG9CQUFvQjtRQUN4QnZDLFVBQVU7UUFDVkUsbUJBQW1CO1FBQ25CTSxjQUFjLENBQUNTLE9BQVU7Z0JBQUUsR0FBR0EsSUFBSTtnQkFBRVIsTUFBTTtZQUFFO0lBQzlDO0lBRUEsTUFBTStCLGlCQUFpQjtRQUFFQyxTQUFTO1FBQVFDLFFBQVE7SUFBaUI7SUFDbkUsTUFBTUMsY0FBYztRQUNsQkYsU0FBUztRQUNUQyxRQUFRO1FBQ1JFLGNBQWM7UUFDZEMsUUFBUTtRQUNSQyxPQUFPO0lBQ1Q7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUMsT0FBTztZQUFFUCxTQUFTO1FBQU87OzBCQUM1Qiw4REFBQzlDLGtEQUFJQTswQkFDSCw0RUFBQ3NEOzhCQUFNOzs7Ozs7Ozs7OzswQkFHVCw4REFBQ0M7MEJBQUc7Ozs7OzswQkFFSiw4REFBQ0g7Z0JBQUlDLE9BQU87b0JBQUVHLFFBQVE7Z0JBQVM7MEJBQzdCLDRFQUFDQztvQkFDQ0MsU0FBUyxJQUFNeEMsT0FBT3lDLElBQUksQ0FBQztvQkFDM0JOLE9BQU87d0JBQUUsR0FBR0wsV0FBVzt3QkFBRVksaUJBQWlCO3dCQUFXZCxTQUFTO29CQUFXOzhCQUMxRTs7Ozs7Ozs7Ozs7WUFNRnBDLHVCQUNDLDhEQUFDMEM7Z0JBQ0NDLE9BQU87b0JBQ0xGLE9BQU87b0JBQ1BVLGNBQWM7b0JBQ2RmLFNBQVM7b0JBQ1RjLGlCQUFpQjtvQkFDakJiLFFBQVE7Z0JBQ1Y7O2tDQUVBLDhEQUFDZTtrQ0FBTzs7Ozs7O29CQUFlO29CQUFFcEQ7Ozs7Ozs7WUFJNUJGLHdCQUNDLDhEQUFDNEM7Z0JBQUlDLE9BQU87b0JBQUVVLFdBQVc7b0JBQVVQLFFBQVE7Z0JBQVM7O2tDQUNsRCw4REFBQ0o7d0JBQ0NDLE9BQU87NEJBQ0xXLE9BQU87NEJBQ1BDLFFBQVE7NEJBQ1JsQixRQUFROzRCQUNSbUIsV0FBVzs0QkFDWGpCLGNBQWM7NEJBQ2RrQixXQUFXOzRCQUNYWCxRQUFRO3dCQUNWOzs7Ozs7a0NBRUYsOERBQUNIO2tDQUNFLENBQUM7Ozs7O1lBS0YsQ0FBQzs7Ozs7Ozs7Ozs7cUNBSUwsOERBQUNlO2dCQUFNZixPQUFPO29CQUFFVyxPQUFPO29CQUFRSyxnQkFBZ0I7b0JBQVlDLFdBQVc7Z0JBQU87O2tDQUMzRSw4REFBQ0M7a0NBQ0MsNEVBQUNDOzRCQUFHbkIsT0FBTztnQ0FBRU8saUJBQWlCOzRCQUFVOzs4Q0FDdEMsOERBQUNhO29DQUFHcEIsT0FBT1I7OENBQWdCOzs7Ozs7OENBQzNCLDhEQUFDNEI7b0NBQUdwQixPQUFPUjs4Q0FBZ0I7Ozs7Ozs4Q0FDM0IsOERBQUM0QjtvQ0FBR3BCLE9BQU9SOzhDQUFnQjs7Ozs7OzhDQUMzQiw4REFBQzRCO29DQUFHcEIsT0FBT1I7OENBQWdCOzs7Ozs7OENBQzNCLDhEQUFDNEI7b0NBQUdwQixPQUFPUjs4Q0FBZ0I7Ozs7Ozs4Q0FDM0IsOERBQUM0QjtvQ0FBR3BCLE9BQU9SOzhDQUFnQjs7Ozs7OzhDQUMzQiw4REFBQzRCO29DQUFHcEIsT0FBT1I7OENBQWdCOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FHL0IsOERBQUM2QjtrQ0FDRXhFLGdCQUFnQnlFLE1BQU0sR0FBRyxJQUN4QnpFLGdCQUFnQjBFLEdBQUcsQ0FBQyxDQUFDQyxrQkFDbkIsOERBQUNMOztrREFDQyw4REFBQ007d0NBQUd6QixPQUFPUjtrREFBaUJnQyxFQUFFekMsRUFBRTs7Ozs7O2tEQUNoQyw4REFBQzBDO3dDQUFHekIsT0FBT1I7a0RBQWlCZ0MsRUFBRUUsY0FBYzs7Ozs7O2tEQUM1Qyw4REFBQ0Q7d0NBQUd6QixPQUFPUjtrREFBaUJnQyxFQUFFRyxVQUFVOzs7Ozs7a0RBQ3hDLDhEQUFDRjt3Q0FBR3pCLE9BQU9SO2tEQUFpQmdDLEVBQUVJLFlBQVk7Ozs7OztrREFDMUMsOERBQUNIO3dDQUFHekIsT0FBT1I7a0RBQWlCZ0MsRUFBRUssTUFBTSxLQUFLLElBQUksV0FBVzs7Ozs7O2tEQUN4RCw4REFBQ0o7d0NBQUd6QixPQUFPUjtrREFDUmdDLEVBQUVNLFVBQVUsR0FBRyxJQUFJQyxLQUFLUCxFQUFFTSxVQUFVLEVBQUVFLGtCQUFrQixLQUFLOzs7Ozs7a0RBRWhFLDhEQUFDUDt3Q0FBR3pCLE9BQU9SOzswREFDVCw4REFBQ1k7Z0RBQ0NDLFNBQVMsSUFBTXhDLE9BQU95QyxJQUFJLENBQUMsQ0FBQyxzQkFBc0IsRUFBRWtCLEVBQUV6QyxFQUFFLENBQUMsQ0FBQztnREFDMURpQixPQUFPO29EQUFFLEdBQUdMLFdBQVc7b0RBQUVZLGlCQUFpQjtvREFBVzBCLGFBQWE7Z0RBQU07MERBQ3pFOzs7Ozs7MERBR0QsOERBQUM3QjtnREFDQ0MsU0FBUyxJQUFNdkIsYUFBYTBDLEVBQUV6QyxFQUFFO2dEQUNoQ2lCLE9BQU87b0RBQUUsR0FBR0wsV0FBVztvREFBRVksaUJBQWlCO2dEQUFVOzBEQUNyRDs7Ozs7Ozs7Ozs7OzsrQkFuQklpQixFQUFFekMsRUFBRTs7OztzREEwQmYsOERBQUNvQztzQ0FDQyw0RUFBQ007Z0NBQUdTLFNBQVE7Z0NBQUlsQyxPQUFPO29DQUFFUCxTQUFTO29DQUFRaUIsV0FBVztnQ0FBUzswQ0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTM0UsOERBQUNYO2dCQUFJQyxPQUFPO29CQUFFaUIsV0FBVztvQkFBUWtCLFNBQVM7b0JBQVFDLGdCQUFnQjtnQkFBUzs7a0NBQ3pFLDhEQUFDaEM7d0JBQ0NDLFNBQVMsSUFBTWxCLGlCQUFpQixDQUFDO3dCQUNqQ2tELFVBQVU5RSxXQUFXRSxJQUFJLElBQUk7d0JBQzdCdUMsT0FBTzs0QkFBRSxHQUFHTCxXQUFXOzRCQUFFWSxpQkFBaUI7NEJBQVcwQixhQUFhO3dCQUFPO2tDQUMxRTs7Ozs7O2tDQUdELDhEQUFDSzt3QkFBS3RDLE9BQU87NEJBQUV1QyxXQUFXO3dCQUFTOzs0QkFBRzs0QkFDOUJoRixXQUFXRSxJQUFJOzRCQUFDOzRCQUFLRixXQUFXRyxVQUFVOzs7Ozs7O2tDQUVsRCw4REFBQzBDO3dCQUNDQyxTQUFTLElBQU1sQixpQkFBaUI7d0JBQ2hDa0QsVUFBVTlFLFdBQVdFLElBQUksSUFBSUYsV0FBV0csVUFBVTt3QkFDbERzQyxPQUFPOzRCQUFFLEdBQUdMLFdBQVc7NEJBQUVZLGlCQUFpQjs0QkFBV2lDLFlBQVk7d0JBQU87a0NBQ3pFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNVCIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vc3JjL3BhZ2VzL2N1c3RvbXZhcmlhYmxlcy9pbmRleC5qcz9mZjlkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnXHJcbmltcG9ydCBheGlvcyBmcm9tICdheGlvcydcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInXHJcbmltcG9ydCBIZWFkIGZyb20gJ25leHQvaGVhZCdcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEN1c3RvbVZhcmlhYmxlc0xpc3QoKSB7XHJcbiAgY29uc3QgW2N1c3RvbVZhcmlhYmxlcywgc2V0Q3VzdG9tVmFyaWFibGVzXSA9IHVzZVN0YXRlKFtdKVxyXG4gIGNvbnN0IFtzZWFyY2gsIHNldFNlYXJjaF0gPSB1c2VTdGF0ZSgnJylcclxuICBjb25zdCBbZGVib3VuY2VkU2VhcmNoLCBzZXREZWJvdW5jZWRTZWFyY2hdID0gdXNlU3RhdGUoJycpXHJcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXHJcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZShudWxsKVxyXG4gIGNvbnN0IFtwYWdpbmF0aW9uLCBzZXRQYWdpbmF0aW9uXSA9IHVzZVN0YXRlKHtcclxuICAgIHBhZ2U6IDEsXHJcbiAgICB0b3RhbFBhZ2VzOiAxLFxyXG4gICAgdG90YWw6IDAsXHJcbiAgICBsaW1pdDogMTAsXHJcbiAgfSlcclxuXHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcclxuXHJcbiAgLy8gRGVib3VuY2Ugc2VhcmNoIGlucHV0XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGhhbmRsZXIgPSBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgc2V0RGVib3VuY2VkU2VhcmNoKHNlYXJjaClcclxuICAgIH0sIDUwMClcclxuICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQoaGFuZGxlcilcclxuICB9LCBbc2VhcmNoXSlcclxuXHJcbiAgLy8gV2hlbiBkZWJvdW5jZWRTZWFyY2ggY2hhbmdlcyDihpIgcmVzZXQgcGFnZSB0byAxXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIHNldFBhZ2luYXRpb24oKHByZXYpID0+ICh7IC4uLnByZXYsIHBhZ2U6IDEgfSkpXHJcbiAgfSwgW2RlYm91bmNlZFNlYXJjaF0pXHJcblxyXG4gIGNvbnN0IGZldGNoQ3VzdG9tVmFyaWFibGVzID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xyXG4gICAgc2V0TG9hZGluZyh0cnVlKVxyXG4gICAgc2V0RXJyb3IobnVsbClcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXMgPSBhd2FpdCBheGlvcy5nZXQoJy9hcGkvY3VzdG9tdmFyaWFibGVzJywge1xyXG4gICAgICAgIHBhcmFtczoge1xyXG4gICAgICAgICAgc2VhcmNoOiBkZWJvdW5jZWRTZWFyY2gsXHJcbiAgICAgICAgICBwYWdlOiBwYWdpbmF0aW9uLnBhZ2UsXHJcbiAgICAgICAgICBsaW1pdDogcGFnaW5hdGlvbi5saW1pdCxcclxuICAgICAgICB9LFxyXG4gICAgICB9KVxyXG5cclxuICAgICAgY29uc3QgZGF0YSA9IHJlcy5kYXRhPy5kYXRhIHx8IHJlcy5kYXRhXHJcbiAgICAgIGlmICghQXJyYXkuaXNBcnJheShkYXRhKSkgdGhyb3cgbmV3IEVycm9yKCdVbmV4cGVjdGVkIHJlc3BvbnNlIGZvcm1hdCcpXHJcblxyXG4gICAgICBzZXRDdXN0b21WYXJpYWJsZXMoZGF0YSlcclxuICAgICAgaWYgKHJlcy5kYXRhLnBhZ2luYXRpb24pIHtcclxuICAgICAgICBzZXRQYWdpbmF0aW9uKChwcmV2KSA9PiAoe1xyXG4gICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgIHRvdGFsUGFnZXM6IHJlcy5kYXRhLnBhZ2luYXRpb24udG90YWxQYWdlcyB8fCAxLFxyXG4gICAgICAgICAgdG90YWw6IHJlcy5kYXRhLnBhZ2luYXRpb24udG90YWwgfHwgMCxcclxuICAgICAgICB9KSlcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyKSB7XHJcbiAgICAgIGNvbnN0IG1lc3NhZ2UgPVxyXG4gICAgICAgIGVyci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fFxyXG4gICAgICAgIGVyci5yZXNwb25zZT8uc3RhdHVzVGV4dCB8fFxyXG4gICAgICAgIGVyci5tZXNzYWdlIHx8XHJcbiAgICAgICAgJ0Vycm9yIGZldGNoaW5nIGRhdGEnXHJcbiAgICAgIHNldEVycm9yKG1lc3NhZ2UpXHJcbiAgICAgIHNldEN1c3RvbVZhcmlhYmxlcyhbXSlcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXHJcbiAgICB9XHJcbiAgfSwgW2RlYm91bmNlZFNlYXJjaCwgcGFnaW5hdGlvbi5wYWdlLCBwYWdpbmF0aW9uLmxpbWl0XSlcclxuXHJcbiAgLy8gV2F0Y2ggZGVib3VuY2VkU2VhcmNoICsgcGFnZSwgYW5kIGZldGNoIGRhdGFcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgZmV0Y2hDdXN0b21WYXJpYWJsZXMoKVxyXG4gIH0sIFtmZXRjaEN1c3RvbVZhcmlhYmxlc10pXHJcblxyXG4gIGNvbnN0IGhhbmRsZURlbGV0ZSA9IGFzeW5jIChpZCkgPT4ge1xyXG4gICAgaWYgKCFjb25maXJtKCdBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gZGVsZXRlIHRoaXMgY3VzdG9tIHZhcmlhYmxlPycpKSByZXR1cm5cclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRMb2FkaW5nKHRydWUpXHJcbiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGF4aW9zLmRlbGV0ZShgL2FwaS9jdXN0b212YXJpYWJsZXMvJHtpZH1gKVxyXG4gICAgICBhbGVydChyZXMuZGF0YS5tZXNzYWdlIHx8ICdEZWxldGVkIHN1Y2Nlc3NmdWxseScpXHJcbiAgICAgIGZldGNoQ3VzdG9tVmFyaWFibGVzKClcclxuICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICBhbGVydChlcnIucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgZXJyLm1lc3NhZ2UgfHwgJ0Vycm9yIGRlbGV0aW5nJylcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBjb25zdCBoYW5kbGVQYWdlQ2hhbmdlID0gKGRpcmVjdGlvbikgPT4ge1xyXG4gICAgc2V0UGFnaW5hdGlvbigocHJldikgPT4gKHtcclxuICAgICAgLi4ucHJldixcclxuICAgICAgcGFnZTogTWF0aC5tYXgoMSwgcHJldi5wYWdlICsgZGlyZWN0aW9uKSxcclxuICAgIH0pKVxyXG4gIH1cclxuXHJcbiAgY29uc3QgaGFuZGxlQ2xlYXJTZWFyY2ggPSAoKSA9PiB7XHJcbiAgICBzZXRTZWFyY2goJycpXHJcbiAgICBzZXREZWJvdW5jZWRTZWFyY2goJycpXHJcbiAgICBzZXRQYWdpbmF0aW9uKChwcmV2KSA9PiAoeyAuLi5wcmV2LCBwYWdlOiAxIH0pKVxyXG4gIH1cclxuXHJcbiAgY29uc3QgdGFibGVDZWxsU3R5bGUgPSB7IHBhZGRpbmc6ICcxMnB4JywgYm9yZGVyOiAnMXB4IHNvbGlkICNkZGQnIH1cclxuICBjb25zdCBidXR0b25TdHlsZSA9IHtcclxuICAgIHBhZGRpbmc6ICc2cHggMTJweCcsXHJcbiAgICBib3JkZXI6ICdub25lJyxcclxuICAgIGJvcmRlclJhZGl1czogJzRweCcsXHJcbiAgICBjdXJzb3I6ICdwb2ludGVyJyxcclxuICAgIGNvbG9yOiAnd2hpdGUnLFxyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgc3R5bGU9e3sgcGFkZGluZzogJzIwcHgnIH19PlxyXG4gICAgICA8SGVhZD5cclxuICAgICAgICA8dGl0bGU+Q3VzdG9tIFZhcmlhYmxlczwvdGl0bGU+XHJcbiAgICAgIDwvSGVhZD5cclxuXHJcbiAgICAgIDxoMT5DdXN0b20gVmFyaWFibGVzPC9oMT5cclxuXHJcbiAgICAgIDxkaXYgc3R5bGU9e3sgbWFyZ2luOiAnMjBweCAwJyB9fT5cclxuICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnL2N1c3RvbXZhcmlhYmxlcy9jcmVhdGUnKX1cclxuICAgICAgICAgIHN0eWxlPXt7IC4uLmJ1dHRvblN0eWxlLCBiYWNrZ3JvdW5kQ29sb3I6ICcjNENBRjUwJywgcGFkZGluZzogJzhweCAxNnB4JyB9fVxyXG4gICAgICAgID5cclxuICAgICAgICAgIENyZWF0ZSBOZXcgVGVtcGxhdGVcclxuICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICBcclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7ZXJyb3IgJiYgKFxyXG4gICAgICAgIDxkaXZcclxuICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgIGNvbG9yOiAncmVkJyxcclxuICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMXJlbScsXHJcbiAgICAgICAgICAgIHBhZGRpbmc6ICcwLjVyZW0nLFxyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjZmZlZWVlJyxcclxuICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkICNmZmNjY2MnLFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICA8c3Ryb25nPkVycm9yOjwvc3Ryb25nPiB7ZXJyb3J9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICB7bG9hZGluZyA/IChcclxuICAgICAgICA8ZGl2IHN0eWxlPXt7IHRleHRBbGlnbjogJ2NlbnRlcicsIG1hcmdpbjogJzIwcHggMCcgfX0+XHJcbiAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgd2lkdGg6ICc0MHB4JyxcclxuICAgICAgICAgICAgICBoZWlnaHQ6ICc0MHB4JyxcclxuICAgICAgICAgICAgICBib3JkZXI6ICc0cHggc29saWQgI2YzZjNmMycsXHJcbiAgICAgICAgICAgICAgYm9yZGVyVG9wOiAnNHB4IHNvbGlkICMzNDk4ZGInLFxyXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXHJcbiAgICAgICAgICAgICAgYW5pbWF0aW9uOiAnc3BpbiAxcyBsaW5lYXIgaW5maW5pdGUnLFxyXG4gICAgICAgICAgICAgIG1hcmdpbjogJzAgYXV0bycsXHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICAgPHN0eWxlPlxyXG4gICAgICAgICAgICB7YFxyXG4gICAgICAgICAgICAgIEBrZXlmcmFtZXMgc3BpbiB7XHJcbiAgICAgICAgICAgICAgICAwJSB7IHRyYW5zZm9ybTogcm90YXRlKDBkZWcpOyB9XHJcbiAgICAgICAgICAgICAgICAxMDAlIHsgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTsgfVxyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgYH1cclxuICAgICAgICAgIDwvc3R5bGU+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICkgOiAoXHJcbiAgICAgICAgPHRhYmxlIHN0eWxlPXt7IHdpZHRoOiAnMTAwJScsIGJvcmRlckNvbGxhcHNlOiAnY29sbGFwc2UnLCBtYXJnaW5Ub3A6ICcyMHB4JyB9fT5cclxuICAgICAgICAgIDx0aGVhZD5cclxuICAgICAgICAgICAgPHRyIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogJyNmMmYyZjInIH19PlxyXG4gICAgICAgICAgICAgIDx0aCBzdHlsZT17dGFibGVDZWxsU3R5bGV9PklEPC90aD5cclxuICAgICAgICAgICAgICA8dGggc3R5bGU9e3RhYmxlQ2VsbFN0eWxlfT5WYXJpYWJsZSBOYW1lPC90aD5cclxuICAgICAgICAgICAgICA8dGggc3R5bGU9e3RhYmxlQ2VsbFN0eWxlfT5UYWJsZSBOYW1lPC90aD5cclxuICAgICAgICAgICAgICA8dGggc3R5bGU9e3RhYmxlQ2VsbFN0eWxlfT5UYWJsZSBDb2x1bW48L3RoPlxyXG4gICAgICAgICAgICAgIDx0aCBzdHlsZT17dGFibGVDZWxsU3R5bGV9PlN0YXR1czwvdGg+XHJcbiAgICAgICAgICAgICAgPHRoIHN0eWxlPXt0YWJsZUNlbGxTdHlsZX0+Q3JlYXRlZCBBdDwvdGg+XHJcbiAgICAgICAgICAgICAgPHRoIHN0eWxlPXt0YWJsZUNlbGxTdHlsZX0+QWN0aW9uczwvdGg+XHJcbiAgICAgICAgICAgIDwvdHI+XHJcbiAgICAgICAgICA8L3RoZWFkPlxyXG4gICAgICAgICAgPHRib2R5PlxyXG4gICAgICAgICAgICB7Y3VzdG9tVmFyaWFibGVzLmxlbmd0aCA+IDAgPyAoXHJcbiAgICAgICAgICAgICAgY3VzdG9tVmFyaWFibGVzLm1hcCgodikgPT4gKFxyXG4gICAgICAgICAgICAgICAgPHRyIGtleT17di5pZH0+XHJcbiAgICAgICAgICAgICAgICAgIDx0ZCBzdHlsZT17dGFibGVDZWxsU3R5bGV9Pnt2LmlkfTwvdGQ+XHJcbiAgICAgICAgICAgICAgICAgIDx0ZCBzdHlsZT17dGFibGVDZWxsU3R5bGV9Pnt2LmN1c3RfdmFyaV9uYW1lfTwvdGQ+XHJcbiAgICAgICAgICAgICAgICAgIDx0ZCBzdHlsZT17dGFibGVDZWxsU3R5bGV9Pnt2LnRhYmxlX25hbWV9PC90ZD5cclxuICAgICAgICAgICAgICAgICAgPHRkIHN0eWxlPXt0YWJsZUNlbGxTdHlsZX0+e3YudGFibGVfY29sdW1ufTwvdGQ+XHJcbiAgICAgICAgICAgICAgICAgIDx0ZCBzdHlsZT17dGFibGVDZWxsU3R5bGV9Pnt2LnN0YXR1cyA9PT0gMSA/ICdBY3RpdmUnIDogJ0luYWN0aXZlJ308L3RkPlxyXG4gICAgICAgICAgICAgICAgICA8dGQgc3R5bGU9e3RhYmxlQ2VsbFN0eWxlfT5cclxuICAgICAgICAgICAgICAgICAgICB7di5jcmVhdGVkX2F0ID8gbmV3IERhdGUodi5jcmVhdGVkX2F0KS50b0xvY2FsZURhdGVTdHJpbmcoKSA6ICdOL0EnfVxyXG4gICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICA8dGQgc3R5bGU9e3RhYmxlQ2VsbFN0eWxlfT5cclxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaChgL2N1c3RvbXZhcmlhYmxlcy9lZGl0LyR7di5pZH1gKX1cclxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IC4uLmJ1dHRvblN0eWxlLCBiYWNrZ3JvdW5kQ29sb3I6ICcjMjE5NkYzJywgbWFyZ2luUmlnaHQ6ICc4cHgnIH19XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgRWRpdFxyXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZURlbGV0ZSh2LmlkKX1cclxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IC4uLmJ1dHRvblN0eWxlLCBiYWNrZ3JvdW5kQ29sb3I6ICcjZjQ0MzM2JyB9fVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIERlbGV0ZVxyXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgPC90cj5cclxuICAgICAgICAgICAgICApKVxyXG4gICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgIDx0cj5cclxuICAgICAgICAgICAgICAgIDx0ZCBjb2xTcGFuPVwiN1wiIHN0eWxlPXt7IHBhZGRpbmc6ICcxMnB4JywgdGV4dEFsaWduOiAnY2VudGVyJyB9fT5cclxuICAgICAgICAgICAgICAgICAgTm8gY3VzdG9tIHZhcmlhYmxlcyBmb3VuZFxyXG4gICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICA8L3RyPlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC90Ym9keT5cclxuICAgICAgICA8L3RhYmxlPlxyXG4gICAgICApfVxyXG5cclxuICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Ub3A6ICcyMHB4JywgZGlzcGxheTogJ2ZsZXgnLCBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicgfX0+XHJcbiAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlUGFnZUNoYW5nZSgtMSl9XHJcbiAgICAgICAgICBkaXNhYmxlZD17cGFnaW5hdGlvbi5wYWdlIDw9IDF9XHJcbiAgICAgICAgICBzdHlsZT17eyAuLi5idXR0b25TdHlsZSwgYmFja2dyb3VuZENvbG9yOiAnIzIxOTZGMycsIG1hcmdpblJpZ2h0OiAnMTBweCcgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICBQcmV2aW91c1xyXG4gICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgIDxzcGFuIHN0eWxlPXt7IGFsaWduU2VsZjogJ2NlbnRlcicgfX0+XHJcbiAgICAgICAgICBQYWdlIHtwYWdpbmF0aW9uLnBhZ2V9IG9mIHtwYWdpbmF0aW9uLnRvdGFsUGFnZXN9XHJcbiAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgIDxidXR0b25cclxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVBhZ2VDaGFuZ2UoMSl9XHJcbiAgICAgICAgICBkaXNhYmxlZD17cGFnaW5hdGlvbi5wYWdlID49IHBhZ2luYXRpb24udG90YWxQYWdlc31cclxuICAgICAgICAgIHN0eWxlPXt7IC4uLmJ1dHRvblN0eWxlLCBiYWNrZ3JvdW5kQ29sb3I6ICcjMjE5NkYzJywgbWFyZ2luTGVmdDogJzEwcHgnIH19XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgTmV4dFxyXG4gICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gIClcclxufVxyXG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VDYWxsYmFjayIsImF4aW9zIiwidXNlUm91dGVyIiwiSGVhZCIsIkN1c3RvbVZhcmlhYmxlc0xpc3QiLCJjdXN0b21WYXJpYWJsZXMiLCJzZXRDdXN0b21WYXJpYWJsZXMiLCJzZWFyY2giLCJzZXRTZWFyY2giLCJkZWJvdW5jZWRTZWFyY2giLCJzZXREZWJvdW5jZWRTZWFyY2giLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJwYWdpbmF0aW9uIiwic2V0UGFnaW5hdGlvbiIsInBhZ2UiLCJ0b3RhbFBhZ2VzIiwidG90YWwiLCJsaW1pdCIsInJvdXRlciIsImhhbmRsZXIiLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0IiwicHJldiIsImZldGNoQ3VzdG9tVmFyaWFibGVzIiwicmVzIiwiZ2V0IiwicGFyYW1zIiwiZGF0YSIsIkFycmF5IiwiaXNBcnJheSIsIkVycm9yIiwiZXJyIiwibWVzc2FnZSIsInJlc3BvbnNlIiwic3RhdHVzVGV4dCIsImhhbmRsZURlbGV0ZSIsImlkIiwiY29uZmlybSIsImRlbGV0ZSIsImFsZXJ0IiwiaGFuZGxlUGFnZUNoYW5nZSIsImRpcmVjdGlvbiIsIk1hdGgiLCJtYXgiLCJoYW5kbGVDbGVhclNlYXJjaCIsInRhYmxlQ2VsbFN0eWxlIiwicGFkZGluZyIsImJvcmRlciIsImJ1dHRvblN0eWxlIiwiYm9yZGVyUmFkaXVzIiwiY3Vyc29yIiwiY29sb3IiLCJkaXYiLCJzdHlsZSIsInRpdGxlIiwiaDEiLCJtYXJnaW4iLCJidXR0b24iLCJvbkNsaWNrIiwicHVzaCIsImJhY2tncm91bmRDb2xvciIsIm1hcmdpbkJvdHRvbSIsInN0cm9uZyIsInRleHRBbGlnbiIsIndpZHRoIiwiaGVpZ2h0IiwiYm9yZGVyVG9wIiwiYW5pbWF0aW9uIiwidGFibGUiLCJib3JkZXJDb2xsYXBzZSIsIm1hcmdpblRvcCIsInRoZWFkIiwidHIiLCJ0aCIsInRib2R5IiwibGVuZ3RoIiwibWFwIiwidiIsInRkIiwiY3VzdF92YXJpX25hbWUiLCJ0YWJsZV9uYW1lIiwidGFibGVfY29sdW1uIiwic3RhdHVzIiwiY3JlYXRlZF9hdCIsIkRhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJtYXJnaW5SaWdodCIsImNvbFNwYW4iLCJkaXNwbGF5IiwianVzdGlmeUNvbnRlbnQiLCJkaXNhYmxlZCIsInNwYW4iLCJhbGlnblNlbGYiLCJtYXJnaW5MZWZ0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/customvariables/index.js\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcustomvariables&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Ccustomvariables%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();