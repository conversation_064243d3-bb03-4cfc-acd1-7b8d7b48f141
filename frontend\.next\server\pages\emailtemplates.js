/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/emailtemplates";
exports.ids = ["pages/emailtemplates"];
exports.modules = {

/***/ "./src/styles/Layout.module.css":
/*!**************************************!*\
  !*** ./src/styles/Layout.module.css ***!
  \**************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"container\": \"Layout_container__m2XBw\",\n\t\"nav\": \"Layout_nav__BY5_j\",\n\t\"logo\": \"Layout_logo__A8yk9\",\n\t\"menu\": \"Layout_menu__dRwJa\",\n\t\"main\": \"Layout_main__65zHd\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvc3R5bGVzL0xheW91dC5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcm9udGVuZC8uL3NyYy9zdHlsZXMvTGF5b3V0Lm1vZHVsZS5jc3M/ZTM2MSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJjb250YWluZXJcIjogXCJMYXlvdXRfY29udGFpbmVyX19tMlhCd1wiLFxuXHRcIm5hdlwiOiBcIkxheW91dF9uYXZfX0JZNV9qXCIsXG5cdFwibG9nb1wiOiBcIkxheW91dF9sb2dvX19BOHlrOVwiLFxuXHRcIm1lbnVcIjogXCJMYXlvdXRfbWVudV9fZFJ3SmFcIixcblx0XCJtYWluXCI6IFwiTGF5b3V0X21haW5fXzY1ekhkXCJcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/styles/Layout.module.css\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Femailtemplates&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cemailtemplates%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Femailtemplates&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cemailtemplates%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_emailtemplates_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\emailtemplates\\index.js */ \"./src/pages/emailtemplates/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_emailtemplates_index_js__WEBPACK_IMPORTED_MODULE_5__]);\n_src_pages_emailtemplates_index_js__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_index_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_index_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_index_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_emailtemplates_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/emailtemplates\",\n        pathname: \"/emailtemplates\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_emailtemplates_index_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Femailtemplates&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cemailtemplates%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/Layout.module.css */ \"./src/styles/Layout.module.css\");\n/* harmony import */ var _styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction Layout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().container),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().nav),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().logo),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            children: \"My App\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().menu),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/smtp-settings\",\n                                    children: \"SMTP Settings\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/customvariables\",\n                                    children: \"Custom Variables\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/emailtemplates\",\n                                    children: \"Email Templates\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: (_styles_Layout_module_css__WEBPACK_IMPORTED_MODULE_3___default().main),\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout.tsx\");\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE2QjtBQUVXO0FBRXpCLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0UsOERBQUNILDBEQUFNQTtrQkFDTCw0RUFBQ0U7WUFBVyxHQUFHQyxTQUFTOzs7Ozs7Ozs7OztBQUc5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICdAL3N0eWxlcy9nbG9iYWxzLmNzcydcbmltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCdcbmltcG9ydCBMYXlvdXQgZnJvbSAnQC9jb21wb25lbnRzL0xheW91dCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8TGF5b3V0PlxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgIDwvTGF5b3V0PlxuICApXG59XG4iXSwibmFtZXMiOlsiTGF5b3V0IiwiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/emailtemplates/index.js":
/*!*******************************************!*\
  !*** ./src/pages/emailtemplates/index.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EmailTemplatesList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_3__]);\naxios__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nfunction EmailTemplatesList() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        totalPages: 1,\n        total: 0,\n        limit: 10\n    });\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const BACKEND_URL = \"http://localhost:3001\" || 0;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchTemplates(pagination.page);\n    }, [\n        pagination.page\n    ]);\n    const fetchTemplates = async (page)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const { data } = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(`${BACKEND_URL}/api/emailtemplates`, {\n                params: {\n                    page,\n                    limit: pagination.limit\n                }\n            });\n            if (data.success) {\n                setTemplates(data.data);\n                setPagination(data.pagination);\n            } else {\n                setError(data.message || \"Failed to fetch email templates\");\n            }\n        } catch (err) {\n            console.error(\"Error fetching email templates:\", err);\n            setError(`Error: ${err.message}`);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm(\"Are you sure you want to delete this template?\")) return;\n        try {\n            setDeletingId(id);\n            const { data } = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"][\"delete\"](`${BACKEND_URL}/api/emailtemplates/${id}`);\n            if (data.success) {\n                alert(\"Template deleted successfully\");\n                fetchTemplates(pagination.page);\n            } else {\n                alert(data.message || \"Failed to delete template\");\n            }\n        } catch (err) {\n            console.error(\"Error deleting template:\", err);\n            alert(`Error: ${err.response?.data?.message || err.message}`);\n        } finally{\n            setDeletingId(null);\n        }\n    };\n    const handleSync = async (templateId, button = null)=>{\n        if (!templateId) {\n            alert(\"Missing Template ID\"); // Alert before modal\n            return;\n        }\n        alert(\"Starting sync...\"); // Alert before modal\n        if (button) {\n            button.disabled = true;\n            button.innerHTML = \"Syncing...\";\n        }\n        try {\n            const { data } = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(`${BACKEND_URL}/api/emailtemplates/sync/${templateId}`);\n            if (data.success) {\n                alert(\"Template synced successfully\"); // Alert before success modal\n                fetchTemplates(pagination.page);\n            } else {\n                alert(\"Sync failed\"); // Alert before error modal\n                fetchTemplates(pagination.page);\n            }\n        } catch (error) {\n            alert(\"Sync request failed\"); // Alert before error modal\n        } finally{\n            if (button) {\n                button.disabled = false;\n                button.innerHTML = `<i class=\"fa-solid fa-cloud-arrow-up\"></i>`;\n            }\n        }\n    };\n    const handleSend = async (templateId)=>{\n        const confirmed = window.confirm(\"Are you sure you want to send a email for this template?\");\n        if (!confirmed) return;\n        alert(\"Sending… Please wait while the email is being sent.\");\n        try {\n            const { data } = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(`${BACKEND_URL}/api/emailtemplates/send-email/${templateId}`);\n            if (data.success) {\n                alert(\"Email sent successfully\");\n                fetchTemplates(pagination.page); // refresh list if you want\n            } else {\n                alert(data.message || \"Email failed\");\n                fetchTemplates(pagination.page);\n            }\n        } catch (err) {\n            alert(`Something went wrong: ${err.response?.data?.message || err.message || \"Unknown error\"}`);\n        }\n    };\n    const handlePageChange = (newPage)=>{\n        if (newPage >= 1 && newPage <= pagination.totalPages) {\n            setPagination((prev)=>({\n                    ...prev,\n                    page: newPage\n                }));\n        }\n    };\n    const checkBackendStatus = async ()=>{\n        try {\n            setError(\"Checking backend status...\");\n            const { data } = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(`${BACKEND_URL}/api/test`, {\n                timeout: 5000\n            });\n            if (data.message) {\n                setError(`Backend OK: ${data.message}`);\n                try {\n                    const routesRes = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(`${BACKEND_URL}/api/routes`, {\n                        timeout: 5000\n                    });\n                    if (routesRes.data.routes) {\n                        setError(`Backend routes: ${routesRes.data.routes.join(\", \")}`);\n                    }\n                } catch  {\n                // Ignore routes error\n                }\n                fetchTemplates(pagination.page);\n            } else {\n                setError(\"Backend running but returned unexpected response\");\n            }\n        } catch (err) {\n            console.error(\"Error checking backend status:\", err);\n            setError(`Backend error: ${err.message}. Check server at ${BACKEND_URL}`);\n        }\n    };\n    const tableCellStyle = {\n        padding: \"12px\",\n        border: \"1px solid #ddd\"\n    };\n    const buttonStyle = (bg, disabled = false)=>({\n            padding: \"8px 16px\",\n            backgroundColor: disabled ? \"#ccc\" : bg,\n            color: \"white\",\n            border: \"none\",\n            borderRadius: \"4px\",\n            cursor: disabled ? \"default\" : \"pointer\",\n            margin: \"0 4px\"\n        });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"20px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: \"Email Templates\"\n            }, void 0, false, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    margin: \"20px 0\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>router.push(\"/emailtemplates/create\"),\n                    style: buttonStyle(\"#4CAF50\"),\n                    children: \"Create New Template\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: \"red\",\n                    marginBottom: \"20px\"\n                },\n                children: error\n            }, void 0, false, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                lineNumber: 184,\n                columnNumber: 17\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"Loading email templates...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        style: {\n                            width: \"100%\",\n                            borderCollapse: \"collapse\",\n                            marginTop: \"20px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    style: {\n                                        backgroundColor: \"#f2f2f2\"\n                                    },\n                                    children: [\n                                        \"ID\",\n                                        \"Name\",\n                                        \"Subject\",\n                                        \"To\",\n                                        \"Created At\",\n                                        \"Actions\"\n                                    ].map((header)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            style: {\n                                                padding: \"12px\",\n                                                border: \"1px solid #ddd\",\n                                                textAlign: \"left\"\n                                            },\n                                            children: header\n                                        }, header, false, {\n                                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                            lineNumber: 194,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                children: templates.length > 0 ? templates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        style: {\n                                            borderBottom: \"1px solid #ddd\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: tableCellStyle,\n                                                children: template.id\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                                lineNumber: 204,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: tableCellStyle,\n                                                children: template.template_name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                                lineNumber: 205,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: tableCellStyle,\n                                                children: template.subject\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                                lineNumber: 206,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: tableCellStyle,\n                                                children: template.to\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                                lineNumber: 207,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: tableCellStyle,\n                                                children: new Date(template.created_at).toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                                lineNumber: 208,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: tableCellStyle,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>router.push(`/emailtemplates/edit/${template.id}`),\n                                                        style: buttonStyle(\"#2196F3\"),\n                                                        children: \"Edit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDelete(template.id),\n                                                        style: buttonStyle(\"#f44336\", deletingId === template.id),\n                                                        disabled: deletingId === template.id,\n                                                        children: deletingId === template.id ? \"Deleting...\" : \"Delete\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSync(template.id),\n                                                        style: buttonStyle(\"#4CAF50\"),\n                                                        title: \"Sync to SendGrid\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fa-solid fa-cloud-arrow-up\",\n                                                                style: {\n                                                                    marginRight: 5\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Sync\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSend(template.id),\n                                                        style: buttonStyle(\"#2196F3\"),\n                                                        title: \"Send Email\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fa-solid fa-envelope-open-text\",\n                                                                style: {\n                                                                    marginRight: 5\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Send\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                                lineNumber: 211,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, template.id, true, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                        lineNumber: 203,\n                                        columnNumber: 19\n                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        colSpan: \"6\",\n                                        style: {\n                                            padding: \"12px\",\n                                            textAlign: \"center\"\n                                        },\n                                        children: \"No email templates found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                        lineNumber: 246,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                    lineNumber: 245,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    pagination.totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"20px\",\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(pagination.page - 1),\n                                disabled: pagination.page === 1,\n                                style: buttonStyle(\"#2196F3\", pagination.page === 1),\n                                children: \"Previous\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    padding: \"8px 16px\"\n                                },\n                                children: [\n                                    \"Page \",\n                                    pagination.page,\n                                    \" of \",\n                                    pagination.totalPages\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                lineNumber: 263,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(pagination.page + 1),\n                                disabled: pagination.page === pagination.totalPages,\n                                style: buttonStyle(\"#2196F3\", pagination.page === pagination.totalPages),\n                                children: \"Next\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n                        lineNumber: 255,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\emailtemplates\\\\index.js\",\n        lineNumber: 172,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/emailtemplates/index.js\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Femailtemplates&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cemailtemplates%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();