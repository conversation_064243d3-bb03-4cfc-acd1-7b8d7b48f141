/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/smtp-settings"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./src/styles/SmtpSettings.module.css":
/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./src/styles/SmtpSettings.module.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".SmtpSettings_container__vr8HQ {\\n  max-width: 800px;\\n  margin: 0 auto;\\n  padding: 2rem;\\n}\\n\\n.SmtpSettings_title__AvpTa {\\n  font-size: 2rem;\\n  margin-bottom: 2rem;\\n  color: #333;\\n}\\n\\n.SmtpSettings_form__jcGN2 {\\n  background: white;\\n  padding: 2rem;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n\\n.SmtpSettings_formGroup__XOsfn {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.SmtpSettings_formGroup__XOsfn label {\\n  display: block;\\n  margin-bottom: 0.5rem;\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.SmtpSettings_formGroup__XOsfn input {\\n  width: 100%;\\n  padding: 0.75rem;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  font-size: 1rem;\\n}\\n\\n.SmtpSettings_submitButton__kjUsF {\\n  background-color: #0070f3;\\n  color: white;\\n  border: none;\\n  padding: 0.75rem 1.5rem;\\n  border-radius: 4px;\\n  font-size: 1rem;\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n}\\n\\n.SmtpSettings_submitButton__kjUsF:hover {\\n  background-color: #0051b3;\\n}\\n\\n.SmtpSettings_message__NfpjD {\\n  padding: 1rem;\\n  margin-bottom: 1.5rem;\\n  border-radius: 4px;\\n}\\n\\n.SmtpSettings_success__EW97i {\\n  background-color: #d4edda;\\n  color: #155724;\\n  border: 1px solid #c3e6cb;\\n}\\n\\n.SmtpSettings_error__Ivx9n {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n  border: 1px solid #f5c6cb;\\n}\\n\\n.SmtpSettings_loading__cucDR {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100vh;\\n  font-size: 1.2rem;\\n  color: #666;\\n}\\n\\n.SmtpSettings_debugSection__22MJ8 {\\n  margin-top: 2rem;\\n  padding: 1rem;\\n  background-color: #f8f9fa;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n}\\n\\n.SmtpSettings_debugSection__22MJ8 pre {\\n  background-color: #f1f1f1;\\n  padding: 1rem;\\n  border-radius: 4px;\\n  overflow: auto;\\n  max-height: 300px;\\n}\\n\\n.SmtpSettings_debugSection__22MJ8 button {\\n  background-color: #6c757d;\\n  color: white;\\n  border: none;\\n  padding: 0.5rem 1rem;\\n  border-radius: 4px;\\n  margin-bottom: 1rem;\\n  cursor: pointer;\\n}\\n\\n.SmtpSettings_debugSection__22MJ8 button:hover {\\n  background-color: #5a6268;\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/SmtpSettings.module.css\"],\"names\":[],\"mappings\":\"AAAA;EACE,gBAAgB;EAChB,cAAc;EACd,aAAa;AACf;;AAEA;EACE,eAAe;EACf,mBAAmB;EACnB,WAAW;AACb;;AAEA;EACE,iBAAiB;EACjB,aAAa;EACb,kBAAkB;EAClB,yCAAyC;AAC3C;;AAEA;EACE,qBAAqB;AACvB;;AAEA;EACE,cAAc;EACd,qBAAqB;EACrB,gBAAgB;EAChB,WAAW;AACb;;AAEA;EACE,WAAW;EACX,gBAAgB;EAChB,sBAAsB;EACtB,kBAAkB;EAClB,eAAe;AACjB;;AAEA;EACE,yBAAyB;EACzB,YAAY;EACZ,YAAY;EACZ,uBAAuB;EACvB,kBAAkB;EAClB,eAAe;EACf,eAAe;EACf,iCAAiC;AACnC;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,aAAa;EACb,qBAAqB;EACrB,kBAAkB;AACpB;;AAEA;EACE,yBAAyB;EACzB,cAAc;EACd,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;EACzB,cAAc;EACd,yBAAyB;AAC3B;;AAEA;EACE,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,aAAa;EACb,iBAAiB;EACjB,WAAW;AACb;;AAEA;EACE,gBAAgB;EAChB,aAAa;EACb,yBAAyB;EACzB,sBAAsB;EACtB,kBAAkB;AACpB;;AAEA;EACE,yBAAyB;EACzB,aAAa;EACb,kBAAkB;EAClB,cAAc;EACd,iBAAiB;AACnB;;AAEA;EACE,yBAAyB;EACzB,YAAY;EACZ,YAAY;EACZ,oBAAoB;EACpB,kBAAkB;EAClB,mBAAmB;EACnB,eAAe;AACjB;;AAEA;EACE,yBAAyB;AAC3B\",\"sourcesContent\":[\".container {\\n  max-width: 800px;\\n  margin: 0 auto;\\n  padding: 2rem;\\n}\\n\\n.title {\\n  font-size: 2rem;\\n  margin-bottom: 2rem;\\n  color: #333;\\n}\\n\\n.form {\\n  background: white;\\n  padding: 2rem;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n\\n.formGroup {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.formGroup label {\\n  display: block;\\n  margin-bottom: 0.5rem;\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.formGroup input {\\n  width: 100%;\\n  padding: 0.75rem;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  font-size: 1rem;\\n}\\n\\n.submitButton {\\n  background-color: #0070f3;\\n  color: white;\\n  border: none;\\n  padding: 0.75rem 1.5rem;\\n  border-radius: 4px;\\n  font-size: 1rem;\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n}\\n\\n.submitButton:hover {\\n  background-color: #0051b3;\\n}\\n\\n.message {\\n  padding: 1rem;\\n  margin-bottom: 1.5rem;\\n  border-radius: 4px;\\n}\\n\\n.success {\\n  background-color: #d4edda;\\n  color: #155724;\\n  border: 1px solid #c3e6cb;\\n}\\n\\n.error {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n  border: 1px solid #f5c6cb;\\n}\\n\\n.loading {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100vh;\\n  font-size: 1.2rem;\\n  color: #666;\\n}\\n\\n.debugSection {\\n  margin-top: 2rem;\\n  padding: 1rem;\\n  background-color: #f8f9fa;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n}\\n\\n.debugSection pre {\\n  background-color: #f1f1f1;\\n  padding: 1rem;\\n  border-radius: 4px;\\n  overflow: auto;\\n  max-height: 300px;\\n}\\n\\n.debugSection button {\\n  background-color: #6c757d;\\n  color: white;\\n  border: none;\\n  padding: 0.5rem 1rem;\\n  border-radius: 4px;\\n  margin-bottom: 1rem;\\n  cursor: pointer;\\n}\\n\\n.debugSection button:hover {\\n  background-color: #5a6268;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"container\": \"SmtpSettings_container__vr8HQ\",\n\t\"title\": \"SmtpSettings_title__AvpTa\",\n\t\"form\": \"SmtpSettings_form__jcGN2\",\n\t\"formGroup\": \"SmtpSettings_formGroup__XOsfn\",\n\t\"submitButton\": \"SmtpSettings_submitButton__kjUsF\",\n\t\"message\": \"SmtpSettings_message__NfpjD\",\n\t\"success\": \"SmtpSettings_success__EW97i\",\n\t\"error\": \"SmtpSettings_error__Ivx9n\",\n\t\"loading\": \"SmtpSettings_loading__cucDR\",\n\t\"debugSection\": \"SmtpSettings_debugSection__22MJ8\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./src/styles/SmtpSettings.module.css\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cwamp%5Cwww%5Cprakash%5Cnode-js-email-template%5Cfrontend%5Csrc%5Cpages%5Csmtp-settings.tsx&page=%2Fsmtp-settings!":
/*!****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cwamp%5Cwww%5Cprakash%5Cnode-js-email-template%5Cfrontend%5Csrc%5Cpages%5Csmtp-settings.tsx&page=%2Fsmtp-settings! ***!
  \****************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/smtp-settings\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/smtp-settings.tsx */ \"./src/pages/smtp-settings.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/smtp-settings\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1EJTNBJTVDd2FtcCU1Q3d3dyU1Q3ByYWthc2glNUNub2RlLWpzLWVtYWlsLXRlbXBsYXRlJTVDZnJvbnRlbmQlNUNzcmMlNUNwYWdlcyU1Q3NtdHAtc2V0dGluZ3MudHN4JnBhZ2U9JTJGc210cC1zZXR0aW5ncyEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyxvRUFBK0I7QUFDdEQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzcwOTMiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi9zbXRwLXNldHRpbmdzXCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwiLi9zcmMvcGFnZXMvc210cC1zZXR0aW5ncy50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL3NtdHAtc2V0dGluZ3NcIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cwamp%5Cwww%5Cprakash%5Cnode-js-email-template%5Cfrontend%5Csrc%5Cpages%5Csmtp-settings.tsx&page=%2Fsmtp-settings!\n"));

/***/ }),

/***/ "./src/styles/SmtpSettings.module.css":
/*!********************************************!*\
  !*** ./src/styles/SmtpSettings.module.css ***!
  \********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./SmtpSettings.module.css */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./src/styles/SmtpSettings.module.css\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./SmtpSettings.module.css */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./src/styles/SmtpSettings.module.css\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./SmtpSettings.module.css */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./src/styles/SmtpSettings.module.css\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/styles/SmtpSettings.module.css\n"));

/***/ }),

/***/ "./src/pages/smtp-settings.tsx":
/*!*************************************!*\
  !*** ./src/pages/smtp-settings.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SmtpSettings; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/api */ \"./src/services/api.ts\");\n/* harmony import */ var _styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../styles/SmtpSettings.module.css */ \"./src/styles/SmtpSettings.module.css\");\n/* harmony import */ var _styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction SubmitButton(param) {\n    let { isConnected, isLoading } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: \"submit\",\n        className: (_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().submitButton),\n        disabled: !isConnected || isLoading,\n        children: isLoading ? \"Saving...\" : \"Save Settings\"\n    }, void 0, false, {\n        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_c = SubmitButton;\nfunction SmtpSettings() {\n    _s();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mail_driver: \"\",\n        mail_host: \"\",\n        mail_port: \"\",\n        mail_username: \"\",\n        mail_password: \"\",\n        mail_from_name: \"\",\n        mail_from_email: \"\",\n        sendgrid_api_key: \"\"\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        text: \"\",\n        type: \"\"\n    });\n    const [backendStatus, setBackendStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isConnected: false,\n        message: \"Checking connection...\",\n        lastChecked: null\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkBackendConnection();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (backendStatus.isConnected) {\n            fetchSettings();\n        }\n    }, [\n        backendStatus.isConnected\n    ]);\n    const checkBackendConnection = async ()=>{\n        try {\n            setBackendStatus((prev)=>({\n                    ...prev,\n                    message: \"Checking connection...\"\n                }));\n            const isAvailable = await _services_api__WEBPACK_IMPORTED_MODULE_3__.api.checkBackend();\n            setBackendStatus({\n                isConnected: isAvailable,\n                message: isAvailable ? \"Connected to backend\" : \"Backend server is not responding\",\n                lastChecked: new Date().toISOString()\n            });\n        } catch (error) {\n            console.error(\"Backend connection check failed:\", error);\n            setBackendStatus({\n                isConnected: false,\n                message: \"Cannot connect to backend: \".concat(error instanceof Error ? error.message : \"Unknown error\"),\n                lastChecked: new Date().toISOString()\n            });\n        }\n    };\n    const fetchSettings = async ()=>{\n        if (!backendStatus.isConnected) {\n            setMessage({\n                text: \"Cannot fetch settings: Backend is not connected\",\n                type: \"error\"\n            });\n            return;\n        }\n        try {\n            setLoading(true);\n            setMessage({\n                text: \"Loading settings...\",\n                type: \"info\"\n            });\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.api.get(\"/api/smtp-settings\");\n            if (response.success && response.data) {\n                setSettings(response.data);\n                setMessage({\n                    text: \"Settings loaded successfully\",\n                    type: \"success\"\n                });\n            } else {\n                setMessage({\n                    text: response.message || \"Failed to load settings\",\n                    type: \"error\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching settings:\", error);\n            setMessage({\n                text: \"Failed to load settings: \".concat(error instanceof Error ? error.message : \"Unknown error\"),\n                type: \"error\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setSettings((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!backendStatus.isConnected) {\n            setMessage({\n                text: \"Cannot save settings: Backend is not connected\",\n                type: \"error\"\n            });\n            return;\n        }\n        setMessage({\n            text: \"Saving settings...\",\n            type: \"info\"\n        });\n        setLoading(true);\n        try {\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.api.post(\"/api/smtp-settings\", settings);\n            if (response.success) {\n                setMessage({\n                    text: \"Settings saved successfully\",\n                    type: \"success\"\n                });\n            } else {\n                setMessage({\n                    text: response.message || \"Failed to save settings\",\n                    type: \"error\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Error saving settings:\", error);\n            setMessage({\n                text: \"Failed to save settings: \".concat(error instanceof Error ? error.message : \"Unknown error\"),\n                type: \"error\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SMTP Settings\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Configure SMTP settings\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().container),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: (_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().title),\n                        children: \"SMTP Settings\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    !backendStatus.isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat((_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().message), \" \").concat((_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().error)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Backend Connection Error:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            \" \",\n                            backendStatus.message,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().connectionHelp),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Please make sure:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"The backend server is running on port 3001\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"There are no firewall or network issues\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"The CORS settings are properly configured\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: checkBackendConnection,\n                                        className: (_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().retryButton),\n                                        children: \"Retry Connection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this),\n                    message.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat((_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().message), \" \").concat((_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default())[message.type]),\n                        children: message.text\n                    }, void 0, false, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: (_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().form),\n                        children: [\n                            [\n                                {\n                                    label: \"Mail Driver\",\n                                    name: \"mail_driver\",\n                                    type: \"text\"\n                                },\n                                {\n                                    label: \"Mail Host\",\n                                    name: \"mail_host\",\n                                    type: \"text\"\n                                },\n                                {\n                                    label: \"Mail Port\",\n                                    name: \"mail_port\",\n                                    type: \"text\"\n                                },\n                                {\n                                    label: \"Mail Username\",\n                                    name: \"mail_username\",\n                                    type: \"text\"\n                                },\n                                {\n                                    label: \"Mail Password\",\n                                    name: \"mail_password\",\n                                    type: \"password\"\n                                },\n                                {\n                                    label: \"From Name\",\n                                    name: \"mail_from_name\",\n                                    type: \"text\"\n                                },\n                                {\n                                    label: \"From Email\",\n                                    name: \"mail_from_email\",\n                                    type: \"email\"\n                                },\n                                {\n                                    label: \"SendGrid API Key (Optional)\",\n                                    name: \"sendgrid_api_key\",\n                                    type: \"password\"\n                                }\n                            ].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().formGroup),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: field.name,\n                                            children: field.label\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: field.type,\n                                            id: field.name,\n                                            name: field.name,\n                                            value: settings[field.name],\n                                            onChange: handleChange,\n                                            required: [\n                                                \"mail_driver\",\n                                                \"mail_host\",\n                                                \"mail_port\",\n                                                \"mail_username\",\n                                                \"mail_password\"\n                                            ].includes(field.name),\n                                            disabled: !backendStatus.isConnected || loading\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, field.name, true, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_SmtpSettings_module_css__WEBPACK_IMPORTED_MODULE_4___default().formActions),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SubmitButton, {\n                                    isConnected: backendStatus.isConnected,\n                                    isLoading: loading\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\wamp\\\\www\\\\prakash\\\\node-js-email-template\\\\frontend\\\\src\\\\pages\\\\smtp-settings.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(SmtpSettings, \"Jk2wD2x1Em5kIVDQiPcSrNjAqos=\");\n_c1 = SmtpSettings;\nvar _c, _c1;\n$RefreshReg$(_c, \"SubmitButton\");\n$RefreshReg$(_c1, \"SmtpSettings\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/smtp-settings.tsx\n"));

/***/ }),

/***/ "./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: function() { return /* binding */ api; }\n/* harmony export */ });\n// API service for making requests to the backend\n// Use environment variable for backend URL or default to localhost:3001\nconst BASE_URL = \"http://localhost:3001\" || 0;\n// Helper function to check if the backend is available\nconst checkBackendAvailability = async ()=>{\n    try {\n        // Create a controller to set a timeout\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 5000);\n        const response = await fetch(\"\".concat(BASE_URL, \"/api/test\"), {\n            method: \"GET\",\n            headers: {\n                \"Accept\": \"application/json\"\n            },\n            signal: controller.signal,\n            // Add cache: 'no-store' to prevent caching\n            cache: \"no-store\"\n        });\n        clearTimeout(timeoutId);\n        if (response.ok) {\n            return true;\n        }\n        console.error(\"Backend check failed with status: \".concat(response.status));\n        return false;\n    } catch (error) {\n        console.error(\"Backend availability check failed:\", error);\n        return false;\n    }\n};\nconst api = {\n    // Export the function with the correct name\n    checkBackend: checkBackendAvailability,\n    get: async (url)=>{\n        try {\n            // Add timeout to the fetch request\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 5000);\n            const res = await fetch(\"\".concat(BASE_URL).concat(url), {\n                headers: {\n                    \"Accept\": \"application/json\"\n                },\n                signal: controller.signal,\n                // Add cache: 'no-store' to prevent caching\n                cache: \"no-store\"\n            });\n            clearTimeout(timeoutId);\n            if (!res.ok) {\n                const errorText = await res.text();\n                console.error(\"API Error (\".concat(res.status, \"):\"), errorText);\n                return {\n                    success: false,\n                    message: \"API Error: \".concat(res.status, \" \").concat(res.statusText),\n                    error: errorText\n                };\n            }\n            const data = await res.json();\n            return data;\n        } catch (error) {\n            console.error(\"API fetch error:\", error);\n            // Check if it's an abort error (timeout)\n            if (error instanceof Error && error.name === \"AbortError\") {\n                return {\n                    success: false,\n                    message: \"Request timed out. Please check if the backend server is running.\"\n                };\n            }\n            return {\n                success: false,\n                message: error instanceof Error ? error.message : \"Failed to fetch\"\n            };\n        }\n    },\n    post: async (url, data)=>{\n        try {\n            // Add timeout to the fetch request\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 5000);\n            const res = await fetch(\"\".concat(BASE_URL).concat(url), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Accept\": \"application/json\"\n                },\n                body: JSON.stringify(data),\n                signal: controller.signal,\n                // Add cache: 'no-store' to prevent caching\n                cache: \"no-store\"\n            });\n            clearTimeout(timeoutId);\n            let responseData;\n            try {\n                responseData = await res.json();\n            } catch (e) {\n                const text = await res.text();\n                responseData = {\n                    success: false,\n                    message: text\n                };\n            }\n            if (!res.ok) {\n                console.error(\"API Error (\".concat(res.status, \"):\"), responseData);\n                return {\n                    success: false,\n                    message: responseData.message || \"API Error: \".concat(res.status, \" \").concat(res.statusText),\n                    error: responseData,\n                    status: res.status\n                };\n            }\n            return responseData;\n        } catch (error) {\n            console.error(\"API post error:\", error);\n            // Check if it's an abort error (timeout)\n            if (error instanceof Error && error.name === \"AbortError\") {\n                return {\n                    success: false,\n                    message: \"Request timed out. Please check if the backend server is running.\"\n                };\n            }\n            return {\n                success: false,\n                message: error instanceof Error ? error.message : \"Failed to fetch\"\n            };\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/services/api.ts\n"));

/***/ }),

/***/ "./node_modules/next/head.js":
/*!***********************************!*\
  !*** ./node_modules/next/head.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/head */ \"./node_modules/next/dist/shared/lib/head.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzIiwibWFwcGluZ3MiOiJBQUFBLGlIQUFrRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzPzg4NDkiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3Qvc2hhcmVkL2xpYi9oZWFkJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/head.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cwamp%5Cwww%5Cprakash%5Cnode-js-email-template%5Cfrontend%5Csrc%5Cpages%5Csmtp-settings.tsx&page=%2Fsmtp-settings!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);